package com.touptek.measurerealize.utils;

import java.lang.System;

/**
 * 🔵 中心圆测量数据 - 专业级圆心+半径点测量
 */
@kotlin.Metadata(mv = {1, 7, 1}, k = 1, d1 = {"\u00008\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0006\n\u0002\b\u0003\n\u0002\u0010\u000b\n\u0000\n\u0002\u0010\b\n\u0002\b\u001c\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0000\b\u0086\b\u0018\u00002\u00020\u0001B]\u0012\f\u0010\u0002\u001a\b\u0012\u0004\u0012\u00020\u00040\u0003\u0012\u0006\u0010\u0005\u001a\u00020\u0006\u0012\u0006\u0010\u0007\u001a\u00020\u0006\u0012\u0006\u0010\b\u001a\u00020\u0006\u0012\b\b\u0002\u0010\t\u001a\u00020\n\u0012\b\b\u0002\u0010\u000b\u001a\u00020\f\u0012\b\b\u0002\u0010\r\u001a\u00020\n\u0012\b\b\u0002\u0010\u000e\u001a\u00020\f\u0012\b\b\u0002\u0010\u000f\u001a\u00020\u0004\u00a2\u0006\u0002\u0010\u0010J\u000f\u0010\u001d\u001a\b\u0012\u0004\u0012\u00020\u00040\u0003H\u00c6\u0003J\t\u0010\u001e\u001a\u00020\u0006H\u00c6\u0003J\t\u0010\u001f\u001a\u00020\u0006H\u00c6\u0003J\t\u0010 \u001a\u00020\u0006H\u00c6\u0003J\t\u0010!\u001a\u00020\nH\u00c6\u0003J\t\u0010\"\u001a\u00020\fH\u00c6\u0003J\t\u0010#\u001a\u00020\nH\u00c6\u0003J\t\u0010$\u001a\u00020\fH\u00c6\u0003J\t\u0010%\u001a\u00020\u0004H\u00c6\u0003Ji\u0010&\u001a\u00020\u00002\u000e\b\u0002\u0010\u0002\u001a\b\u0012\u0004\u0012\u00020\u00040\u00032\b\b\u0002\u0010\u0005\u001a\u00020\u00062\b\b\u0002\u0010\u0007\u001a\u00020\u00062\b\b\u0002\u0010\b\u001a\u00020\u00062\b\b\u0002\u0010\t\u001a\u00020\n2\b\b\u0002\u0010\u000b\u001a\u00020\f2\b\b\u0002\u0010\r\u001a\u00020\n2\b\b\u0002\u0010\u000e\u001a\u00020\f2\b\b\u0002\u0010\u000f\u001a\u00020\u0004H\u00c6\u0001J\u0013\u0010\'\u001a\u00020\n2\b\u0010(\u001a\u0004\u0018\u00010)H\u00d6\u0003J\t\u0010*\u001a\u00020\fH\u00d6\u0001J\t\u0010+\u001a\u00020,H\u00d6\u0001R\u0011\u0010\u0007\u001a\u00020\u0006\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0011\u0010\u0012R\u0011\u0010\u000b\u001a\u00020\f\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0013\u0010\u0014R\u0011\u0010\u000e\u001a\u00020\f\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0015\u0010\u0014R\u0011\u0010\t\u001a\u00020\n\u00a2\u0006\b\n\u0000\u001a\u0004\b\t\u0010\u0016R\u0011\u0010\r\u001a\u00020\n\u00a2\u0006\b\n\u0000\u001a\u0004\b\r\u0010\u0016R\u0011\u0010\b\u001a\u00020\u0006\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0017\u0010\u0012R\u0017\u0010\u0002\u001a\b\u0012\u0004\u0012\u00020\u00040\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0018\u0010\u0019R\u0011\u0010\u0005\u001a\u00020\u0006\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001a\u0010\u0012R\u0011\u0010\u000f\u001a\u00020\u0004\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001b\u0010\u001c\u00a8\u0006-"}, d2 = {"Lcom/touptek/measurerealize/utils/CenterCircleMeasurementData;", "Lcom/touptek/measurerealize/utils/MeasurementData;", "points", "", "Landroid/graphics/PointF;", "radius", "", "area", "perimeter", "isDragging", "", "draggedPointIndex", "", "isPointHighlighted", "highlightedPointIndex", "textPosition", "(Ljava/util/List;DDDZIZILandroid/graphics/PointF;)V", "getArea", "()D", "getDraggedPointIndex", "()I", "getHighlightedPointIndex", "()Z", "getPerimeter", "getPoints", "()Ljava/util/List;", "getRadius", "getTextPosition", "()Landroid/graphics/PointF;", "component1", "component2", "component3", "component4", "component5", "component6", "component7", "component8", "component9", "copy", "equals", "other", "", "hashCode", "toString", "", "app_debug"})
public final class CenterCircleMeasurementData extends com.touptek.measurerealize.utils.MeasurementData {
    @org.jetbrains.annotations.NotNull
    private final java.util.List<android.graphics.PointF> points = null;
    private final double radius = 0.0;
    private final double area = 0.0;
    private final double perimeter = 0.0;
    private final boolean isDragging = false;
    private final int draggedPointIndex = 0;
    private final boolean isPointHighlighted = false;
    private final int highlightedPointIndex = 0;
    @org.jetbrains.annotations.NotNull
    private final android.graphics.PointF textPosition = null;
    
    /**
     * 🔵 中心圆测量数据 - 专业级圆心+半径点测量
     */
    @org.jetbrains.annotations.NotNull
    public final com.touptek.measurerealize.utils.CenterCircleMeasurementData copy(@org.jetbrains.annotations.NotNull
    java.util.List<? extends android.graphics.PointF> points, double radius, double area, double perimeter, boolean isDragging, int draggedPointIndex, boolean isPointHighlighted, int highlightedPointIndex, @org.jetbrains.annotations.NotNull
    android.graphics.PointF textPosition) {
        return null;
    }
    
    /**
     * 🔵 中心圆测量数据 - 专业级圆心+半径点测量
     */
    @java.lang.Override
    public boolean equals(@org.jetbrains.annotations.Nullable
    java.lang.Object other) {
        return false;
    }
    
    /**
     * 🔵 中心圆测量数据 - 专业级圆心+半径点测量
     */
    @java.lang.Override
    public int hashCode() {
        return 0;
    }
    
    /**
     * 🔵 中心圆测量数据 - 专业级圆心+半径点测量
     */
    @org.jetbrains.annotations.NotNull
    @java.lang.Override
    public java.lang.String toString() {
        return null;
    }
    
    public CenterCircleMeasurementData(@org.jetbrains.annotations.NotNull
    java.util.List<? extends android.graphics.PointF> points, double radius, double area, double perimeter, boolean isDragging, int draggedPointIndex, boolean isPointHighlighted, int highlightedPointIndex, @org.jetbrains.annotations.NotNull
    android.graphics.PointF textPosition) {
        super();
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.util.List<android.graphics.PointF> component1() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.util.List<android.graphics.PointF> getPoints() {
        return null;
    }
    
    public final double component2() {
        return 0.0;
    }
    
    public final double getRadius() {
        return 0.0;
    }
    
    public final double component3() {
        return 0.0;
    }
    
    public final double getArea() {
        return 0.0;
    }
    
    public final double component4() {
        return 0.0;
    }
    
    public final double getPerimeter() {
        return 0.0;
    }
    
    public final boolean component5() {
        return false;
    }
    
    public final boolean isDragging() {
        return false;
    }
    
    public final int component6() {
        return 0;
    }
    
    public final int getDraggedPointIndex() {
        return 0;
    }
    
    public final boolean component7() {
        return false;
    }
    
    public final boolean isPointHighlighted() {
        return false;
    }
    
    public final int component8() {
        return 0;
    }
    
    public final int getHighlightedPointIndex() {
        return 0;
    }
    
    @org.jetbrains.annotations.NotNull
    public final android.graphics.PointF component9() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final android.graphics.PointF getTextPosition() {
        return null;
    }
}