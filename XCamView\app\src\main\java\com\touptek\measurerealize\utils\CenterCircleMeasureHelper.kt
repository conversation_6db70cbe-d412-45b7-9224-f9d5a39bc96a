package com.touptek.measurerealize.utils

import android.graphics.Bitmap
import android.graphics.Matrix
import android.graphics.PointF
import android.util.Log
import android.view.ImageView
import android.view.MotionEvent
import com.touptek.xcamview.TpImageView
import com.touptek.measurerealize.utils.CenterCircleMeasurementData
import java.util.*
import kotlin.math.*

/**
 * 🔵 中心圆测量实例 - 专业级数据结构
 * 
 * 设计理念：
 * - 圆心+半径点模式，符合用户直觉
 * - 圆心拖拽移动整个圆，半径点拖拽调整大小
 * - 半径点自动约束到圆周上，确保几何正确性
 */
data class CenterCircleMeasurement(
    val id: String = UUID.randomUUID().toString(),
    var viewPoints: MutableList<PointF> = mutableListOf(),  // [圆心, 半径点] 视图坐标
    var bitmapPoints: MutableList<PointF> = mutableListOf(), // [圆心, 半径点] 位图坐标
    var isSelected: Boolean = false,
    var isEditing: Boolean = false,
    var isCompleted: Boolean = false,
    var textPosition: PointF = PointF(),    // 数值文本位置
    var creationTime: Long = System.currentTimeMillis(),
    var lastModified: Long = System.currentTimeMillis(),
    
    // 缓存计算结果
    private var cachedRadius: Double? = null,
    private var cachedArea: Double? = null,
    private var cachedPerimeter: Double? = null,
    private var cacheValidTime: Long = 0L
) {
    /**
     * 🔵 计算圆的半径（使用位图坐标 - 真实半径，不受缩放影响）
     */
    fun calculateRadius(): Double {
        val currentTime = System.currentTimeMillis()
        
        // 检查缓存是否有效（100ms内）
        if (cachedRadius != null && currentTime - cacheValidTime < 100L) {
            return cachedRadius!!
        }
        
        // 重新计算并缓存
        cachedRadius = computeRadius()
        cacheValidTime = currentTime
        return cachedRadius!!
    }
    
    private fun computeRadius(): Double {
        // 优先使用位图坐标计算真实半径
        val points = if (bitmapPoints.size >= 2) bitmapPoints else viewPoints
        if (points.size < 2) return 0.0
        
        val center = points[0]
        val radiusPoint = points[1]
        
        val dx = radiusPoint.x - center.x
        val dy = radiusPoint.y - center.y
        return sqrt(dx * dx + dy * dy).toDouble()
    }
    
    /**
     * 🔵 计算圆的面积（使用位图坐标 - 真实面积，不受缩放影响）
     */
    fun calculateArea(): Double {
        val currentTime = System.currentTimeMillis()
        
        // 检查缓存是否有效（100ms内）
        if (cachedArea != null && currentTime - cacheValidTime < 100L) {
            return cachedArea!!
        }
        
        // 重新计算并缓存
        val radius = calculateRadius()
        cachedArea = PI * radius * radius
        cacheValidTime = currentTime
        return cachedArea!!
    }
    
    /**
     * 🔵 计算圆的周长（使用位图坐标 - 真实周长，不受缩放影响）
     */
    fun calculatePerimeter(): Double {
        val currentTime = System.currentTimeMillis()
        
        // 检查缓存是否有效（100ms内）
        if (cachedPerimeter != null && currentTime - cacheValidTime < 100L) {
            return cachedPerimeter!!
        }
        
        // 重新计算并缓存
        val radius = calculateRadius()
        cachedPerimeter = 2 * PI * radius
        cacheValidTime = currentTime
        return cachedPerimeter!!
    }
    
    /**
     * 🔵 更新圆心位置（移动整个圆）
     */
    fun updateCenter(newCenter: PointF) {
        if (viewPoints.size < 2) return
        
        val oldCenter = viewPoints[0]
        val dx = newCenter.x - oldCenter.x
        val dy = newCenter.y - oldCenter.y
        
        // 移动圆心和半径点，保持半径不变
        viewPoints[0] = newCenter
        viewPoints[1] = PointF(viewPoints[1].x + dx, viewPoints[1].y + dy)
        
        invalidateCache()
        lastModified = System.currentTimeMillis()
    }
    
    /**
     * 🔵 更新半径点位置（约束到圆周上）
     */
    fun updateRadiusPoint(newPoint: PointF) {
        if (viewPoints.size < 2) return
        
        val center = viewPoints[0]
        val dx = newPoint.x - center.x
        val dy = newPoint.y - center.y
        val length = sqrt(dx * dx + dy * dy)
        
        if (length > 0) {
            // 计算新的半径（用户拖拽的距离）
            val newRadius = length
            
            // 计算单位方向向量
            val unitX = dx / length
            val unitY = dy / length
            
            // 将半径点约束到圆周上
            viewPoints[1] = PointF(
                center.x + (unitX * newRadius).toFloat(),
                center.y + (unitY * newRadius).toFloat()
            )
        }
        
        invalidateCache()
        lastModified = System.currentTimeMillis()
    }
    
    /**
     * 🔵 检查点是否在触摸范围内
     */
    fun isPointInTouchRange(touchPoint: PointF, pointIndex: Int, touchRadius: Float): Boolean {
        if (pointIndex >= viewPoints.size) return false
        
        val point = viewPoints[pointIndex]
        val distance = sqrt((touchPoint.x - point.x).pow(2) + (touchPoint.y - point.y).pow(2))
        return distance <= touchRadius
    }
    
    /**
     * 🔵 获取最近的控制点索引
     */
    fun getNearestPointIndex(touchPoint: PointF): Int {
        if (viewPoints.size < 2) return -1
        
        var nearestIndex = 0
        var minDistance = Float.MAX_VALUE
        
        for (i in viewPoints.indices) {
            val point = viewPoints[i]
            val distance = sqrt((touchPoint.x - point.x).pow(2) + (touchPoint.y - point.y).pow(2))
            if (distance < minDistance) {
                minDistance = distance
                nearestIndex = i
            }
        }
        
        return nearestIndex
    }
    
    /**
     * 🔵 同步位图坐标（从视图坐标转换）
     */
    fun syncBitmapCoords(imageView: ImageView) {
        bitmapPoints.clear()
        viewPoints.forEach { viewPoint ->
            bitmapPoints.add(convertViewToBitmapCoords(viewPoint, imageView))
        }
        invalidateCache()
    }
    
    /**
     * 🔵 同步视图坐标（从位图坐标转换）
     */
    fun syncViewCoords(imageView: ImageView) {
        viewPoints.clear()
        bitmapPoints.forEach { bitmapPoint ->
            viewPoints.add(convertBitmapToViewCoords(bitmapPoint, imageView))
        }
        invalidateCache()
    }
    
    /**
     * 🔵 坐标转换：视图坐标 -> 位图坐标
     */
    private fun convertViewToBitmapCoords(viewPoint: PointF, imageView: ImageView): PointF {
        val matrix = imageView.imageMatrix
        val inverseMatrix = Matrix()
        return if (matrix.invert(inverseMatrix)) {
            val point = FloatArray(2) { 0f }
            point[0] = viewPoint.x
            point[1] = viewPoint.y
            inverseMatrix.mapPoints(point)
            PointF(point[0], point[1])
        } else {
            PointF(viewPoint.x, viewPoint.y)
        }
    }
    
    /**
     * 🔵 坐标转换：位图坐标 -> 视图坐标
     */
    private fun convertBitmapToViewCoords(bitmapPoint: PointF, imageView: ImageView): PointF {
        val matrix = imageView.imageMatrix
        val point = FloatArray(2) { 0f }
        point[0] = bitmapPoint.x
        point[1] = bitmapPoint.y
        matrix.mapPoints(point)
        return PointF(point[0], point[1])
    }
    
    /**
     * 🔵 计算文本显示位置（圆心下方固定偏移）
     */
    fun calculateTextPosition(): PointF {
        if (viewPoints.isEmpty()) return PointF(0f, 0f)
        
        val center = viewPoints[0]
        val radius = if (viewPoints.size >= 2) {
            val radiusPoint = viewPoints[1]
            sqrt((radiusPoint.x - center.x).pow(2) + (radiusPoint.y - center.y).pow(2))
        } else 50f
        
        // 文本放在圆心下方，距离圆周边缘20像素
        return PointF(center.x, center.y + radius + 40f)
    }
    
    /**
     * 🔵 获取显示文本
     */
    fun getDisplayText(): String {
        val radius = calculateRadius()
        val area = calculateArea()
        val perimeter = calculatePerimeter()
        return String.format("半径: %.1f | 面积: %.1f | 周长: %.1f", radius, area, perimeter)
    }
    
    /**
     * 🔵 清除缓存
     */
    private fun invalidateCache() {
        cachedRadius = null
        cachedArea = null
        cachedPerimeter = null
        cacheValidTime = 0L
    }
}

/**
 * 🔵 中心圆测量助手 - 专业级圆形测量实现
 *
 * 核心特性：
 * - 圆心拖拽移动整个圆
 * - 半径点拖拽调整圆的大小，自动约束到圆周
 * - 实时计算半径、面积和周长
 * - 智能触摸检测和拖拽控制
 * - 支持立即拖拽、触摸恢复、长按删除
 */
class CenterCircleMeasureHelper {
    companion object {
        private const val TAG = "CenterCircleMeasureHelper"

        // 交互参数 - 与其他Helper保持一致
        private const val TOUCH_RADIUS = 80f
        private const val CLICK_TOLERANCE = 20f
        private const val LONG_PRESS_DURATION = 800L
    }

    // 核心组件
    private lateinit var imageView: TpImageView
    private lateinit var originalBitmap: Bitmap
    private var isInitialized = false

    // 测量数据
    private val measurements = ArrayList<CenterCircleMeasurement>()
    private var selectedMeasurement: CenterCircleMeasurement? = null

    // 交互状态
    private var isDraggingPoint = false
    private var draggedPointIndex = -1
    private var longPressStartTime = 0L
    private var lastTouchX = 0f
    private var lastTouchY = 0f

    // 回调
    private var measurementUpdateCallback: (() -> Unit)? = null

    /**
     * 🔵 初始化助手
     */
    fun init(imageView: TpImageView, bitmap: Bitmap) {
        this.imageView = imageView
        this.originalBitmap = bitmap.copy(Bitmap.Config.ARGB_8888, true)
        this.isInitialized = true

        Log.d(TAG, "🔵 CenterCircleMeasureHelper initialized")
    }

    /**
     * 🔵 设置测量更新回调
     */
    fun setMeasurementUpdateCallback(callback: () -> Unit) {
        this.measurementUpdateCallback = callback
    }

    /**
     * 🔵 创建新圆形测量
     */
    fun startNewMeasurement(): String {
        if (!isInitialized) {
            Log.w(TAG, "Helper not initialized")
            return ""
        }

        val centerX = imageView.width / 2f
        val centerY = imageView.height / 2f
        val defaultRadius = min(imageView.width, imageView.height) / 6f

        val measurement = CenterCircleMeasurement().apply {
            viewPoints.addAll(listOf(
                PointF(centerX, centerY), // center
                PointF(centerX + defaultRadius, centerY) // radiusPoint
            ))
            isCompleted = true
            isSelected = true
        }

        // 清除其他选中状态
        measurements.forEach {
            it.isSelected = false
            it.isEditing = false
        }

        // 同步位图坐标
        measurement.syncBitmapCoords(imageView)

        // 计算文本位置
        measurement.textPosition = measurement.calculateTextPosition()

        measurements.add(measurement)
        selectedMeasurement = measurement

        measurementUpdateCallback?.invoke()
        Log.d(TAG, "🔵 Created new center circle measurement: ${measurement.id}")
        return measurement.id
    }

    /**
     * 🔵 添加新的圆形测量
     */
    fun addNewCenterCircleMeasurement(): Boolean {
        val measurementId = startNewMeasurement()
        return measurementId.isNotEmpty()
    }

    /**
     * 🔵 获取测量数量
     */
    fun getMeasurementCount(): Int = measurements.size

    /**
     * 🔵 检查是否有选中的测量
     */
    fun hasSelectedMeasurement(): Boolean = selectedMeasurement != null

    /**
     * 🔵 检查是否正在拖拽点
     */
    fun isDraggingPoint(): Boolean = isDraggingPoint

    /**
     * 🔵 检查是否靠近任何测量
     */
    fun isNearAnyMeasurement(touchPoint: PointF): Boolean {
        return measurements.any { measurement ->
            for (pointIndex in 0..1) {
                if (measurement.isPointInTouchRange(touchPoint, pointIndex, TOUCH_RADIUS)) {
                    return true
                }
            }
            false
        }
    }

    /**
     * 🔵 获取所有测量数据
     */
    fun getAllMeasurements(): List<CenterCircleMeasurement> = measurements.toList()

    /**
     * 🔵 清除所有选中状态
     */
    fun clearSelection() {
        measurements.forEach { it.isSelected = false }
        selectedMeasurement = null
        measurementUpdateCallback?.invoke()
    }

    /**
     * 🔵 处理触摸事件 - 核心交互逻辑
     */
    fun handleTouchEvent(event: MotionEvent, viewWidth: Int, viewHeight: Int): Boolean {
        if (!isInitialized) {
            Log.w(TAG, "⚠️ Helper not initialized, ignoring touch event")
            return false
        }

        val touchPoint = PointF(event.x, event.y)
        val x = event.x
        val y = event.y

        when (event.action) {
            MotionEvent.ACTION_DOWN -> {
                longPressStartTime = System.currentTimeMillis()
                lastTouchX = x
                lastTouchY = y

                // 检查控制点触摸（圆心和半径点）
                for (measurement in measurements.reversed()) {
                    for (pointIndex in 0..1) {  // 0=圆心, 1=半径点
                        if (measurement.isPointInTouchRange(touchPoint, pointIndex, TOUCH_RADIUS)) {
                            selectedMeasurement = measurement
                            draggedPointIndex = pointIndex
                            isDraggingPoint = true

                            // 设置选中状态
                            measurements.forEach { it.isSelected = false }
                            measurement.isSelected = true

                            val pointName = if (pointIndex == 0) "center" else "radius point"
                            Log.d(TAG, "🔵 Started dragging $pointName of measurement ${measurement.id}")
                            measurementUpdateCallback?.invoke()
                            return true
                        }
                    }
                }

                // 空白区域点击处理
                if (isInImageContentArea(touchPoint, viewWidth, viewHeight)) {
                    measurements.forEach { it.isSelected = false }
                    selectedMeasurement = null
                    Log.d(TAG, "🔵 Clicked in empty area, cleared selection")
                    measurementUpdateCallback?.invoke()
                }

                return false
            }

            MotionEvent.ACTION_MOVE -> {
                if (isDraggingPoint && selectedMeasurement != null && draggedPointIndex >= 0) {
                    when (draggedPointIndex) {
                        0 -> selectedMeasurement!!.updateCenter(touchPoint)  // 拖动圆心，移动整个圆
                        1 -> selectedMeasurement!!.updateRadiusPoint(touchPoint)  // 拖动半径点，调整大小
                    }
                    selectedMeasurement!!.syncBitmapCoords(imageView)
                    measurementUpdateCallback?.invoke()
                    return true
                }

                // 触摸恢复机制 - 检查是否重新接触到控制点
                else if (!isDraggingPoint && selectedMeasurement != null) {
                    for (measurement in measurements.reversed()) {
                        if (measurement == selectedMeasurement) {
                            for (pointIndex in 0..1) {  // 包含圆心和半径点的触摸恢复
                                if (measurement.isPointInTouchRange(touchPoint, pointIndex, TOUCH_RADIUS)) {
                                    draggedPointIndex = pointIndex
                                    isDraggingPoint = true

                                    // 立即处理这次移动
                                    when (pointIndex) {
                                        0 -> measurement.updateCenter(touchPoint)
                                        1 -> measurement.updateRadiusPoint(touchPoint)
                                    }
                                    measurement.syncBitmapCoords(imageView)
                                    measurementUpdateCallback?.invoke()

                                    val pointName = if (pointIndex == 0) "center" else "radius point"
                                    Log.d(TAG, "🔄 Touch recovery successful - resumed dragging $pointName")
                                    return true
                                }
                            }
                        }
                    }
                }

                return false
            }

            MotionEvent.ACTION_UP -> {
                val touchDuration = System.currentTimeMillis() - longPressStartTime
                val touchDistance = sqrt((x - lastTouchX) * (x - lastTouchX) + (y - lastTouchY) * (y - lastTouchY))
                val wasDragging = isDraggingPoint

                var handled = false

                // 重置拖拽状态
                isDraggingPoint = false
                draggedPointIndex = -1

                if (wasDragging) {
                    // 拖拽完成
                    measurementUpdateCallback?.invoke()
                    handled = true
                }

                // 长按删除
                if (!wasDragging && touchDistance < CLICK_TOLERANCE && touchDuration > LONG_PRESS_DURATION) {
                    for (measurement in measurements.reversed()) {
                        for (pointIndex in 0..1) {
                            if (measurement.isPointInTouchRange(touchPoint, pointIndex, TOUCH_RADIUS)) {
                                measurements.remove(measurement)
                                if (selectedMeasurement == measurement) {
                                    selectedMeasurement = null
                                }
                                Log.d(TAG, "🗑️ Long press deleted measurement: ${measurement.id}")
                                measurementUpdateCallback?.invoke()
                                handled = true
                                break
                            }
                        }
                        if (handled) break
                    }
                }

                // 轻触选中
                if (!wasDragging && !handled && touchDistance < CLICK_TOLERANCE && touchDuration < LONG_PRESS_DURATION) {
                    for (measurement in measurements.reversed()) {
                        for (pointIndex in 0..1) {
                            if (measurement.isPointInTouchRange(touchPoint, pointIndex, TOUCH_RADIUS)) {
                                measurements.forEach { it.isSelected = false }
                                measurement.isSelected = true
                                selectedMeasurement = measurement
                                Log.d(TAG, "🔵 Selected measurement: ${measurement.id}")
                                measurementUpdateCallback?.invoke()
                                return true
                            }
                        }
                    }
                }

                return handled
            }
        }

        return false
    }

    /**
     * 🔵 检查点是否在图像内容区域
     */
    private fun isInImageContentArea(point: PointF, viewWidth: Int, viewHeight: Int): Boolean {
        return point.x >= 0 && point.x <= viewWidth && point.y >= 0 && point.y <= viewHeight
    }

    /**
     * 🔵 删除选中的测量
     */
    fun deleteSelectedMeasurement(): Boolean {
        selectedMeasurement?.let { measurement ->
            measurements.remove(measurement)
            selectedMeasurement = null
            measurementUpdateCallback?.invoke()
            Log.d(TAG, "🗑️ Deleted selected measurement: ${measurement.id}")
            return true
        }
        return false
    }

    /**
     * 🔵 删除所有测量
     */
    fun clearAllMeasurements() {
        measurements.clear()
        selectedMeasurement = null
        measurementUpdateCallback?.invoke()
        Log.d(TAG, "🗑️ Cleared all measurements")
    }

    /**
     * 🔵 缩放变化时同步坐标
     */
    fun onScaleChanged() {
        measurements.forEach { measurement ->
            measurement.syncViewCoords(imageView)
        }
        measurementUpdateCallback?.invoke()
        Log.d(TAG, "🔄 Synced coordinates after scale change")
    }

    /**
     * 🔵 暂停测量（保留数据）
     */
    fun pauseMeasurement() {
        isDraggingPoint = false
        draggedPointIndex = -1
        Log.d(TAG, "⏸️ Measurement paused")
    }

    /**
     * 🔵 恢复测量
     */
    fun resumeMeasurement() {
        Log.d(TAG, "▶️ Measurement resumed")
    }

    /**
     * 🔵 获取选中的测量
     */
    fun getSelectedMeasurement(): CenterCircleMeasurement? = selectedMeasurement

    /**
     * 🔵 检查是否有测量数据
     */
    fun hasMeasurements(): Boolean = measurements.isNotEmpty()

    /**
     * 🔵 获取测量统计信息
     */
    fun getMeasurementStats(): String {
        return "圆形测量: ${measurements.size}个"
    }

    /**
     * 🔵 获取所有测量数据用于覆盖层显示
     */
    fun getAllMeasurementData(): List<CenterCircleMeasurementData> {
        return measurements.mapNotNull { measurement ->
            if (measurement.viewPoints.size >= 2) {
                CenterCircleMeasurementData(
                    points = measurement.viewPoints.toList(),
                    radius = measurement.calculateRadius(),
                    area = measurement.calculateArea(),
                    perimeter = measurement.calculatePerimeter(),
                    isDragging = isDraggingPoint && measurement.isSelected,
                    draggedPointIndex = if (isDraggingPoint && measurement.isSelected) draggedPointIndex else -1,
                    isPointHighlighted = measurement.isSelected && !isDraggingPoint,
                    highlightedPointIndex = if (measurement.isSelected && !isDraggingPoint) 0 else -1,
                    textPosition = measurement.calculateTextPosition()
                )
            } else null
        }
    }

    /**
     * 🔵 获取当前选中的测量数据
     */
    fun getMeasurementData(): CenterCircleMeasurementData? {
        return selectedMeasurement?.let { measurement ->
            if (measurement.viewPoints.size >= 2) {
                CenterCircleMeasurementData(
                    points = measurement.viewPoints.toList(),
                    radius = measurement.calculateRadius(),
                    area = measurement.calculateArea(),
                    perimeter = measurement.calculatePerimeter(),
                    isDragging = isDraggingPoint,
                    draggedPointIndex = if (isDraggingPoint) draggedPointIndex else -1,
                    isPointHighlighted = !isDraggingPoint,
                    highlightedPointIndex = if (!isDraggingPoint) 0 else -1,
                    textPosition = measurement.calculateTextPosition()
                )
            } else null
        }
    }
}
