package com.touptek.measurerealize.utils;

import java.lang.System;

/**
 * 🔵 中心圆测量助手 - 专业级圆形测量实现
 *
 * 核心特性：
 * - 圆心拖拽移动整个圆
 * - 半径点拖拽调整圆的大小，自动约束到圆周
 * - 实时计算半径、面积和周长
 * - 智能触摸检测和拖拽控制
 * - 支持立即拖拽、触摸恢复、长按删除
 */
@kotlin.Metadata(mv = {1, 7, 1}, k = 1, d1 = {"\u0000j\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\b\n\u0002\b\u0004\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0010\u0007\n\u0002\b\u0002\n\u0002\u0010\t\n\u0000\n\u0002\u0018\u0002\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0006\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0010\u000e\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\t\n\u0002\u0018\u0002\n\u0002\b\n\u0018\u0000 ;2\u00020\u0001:\u0001;B\u0005\u00a2\u0006\u0002\u0010\u0002J\u0006\u0010\u0019\u001a\u00020\tJ\u0006\u0010\u001a\u001a\u00020\u0012J\u0006\u0010\u001b\u001a\u00020\u0012J\u0006\u0010\u001c\u001a\u00020\tJ\f\u0010\u001d\u001a\b\u0012\u0004\u0012\u00020\u001f0\u001eJ\f\u0010 \u001a\b\u0012\u0004\u0012\u00020\u00150\u001eJ\u0006\u0010!\u001a\u00020\u0004J\b\u0010\"\u001a\u0004\u0018\u00010\u001fJ\u0006\u0010#\u001a\u00020$J\b\u0010%\u001a\u0004\u0018\u00010\u0015J\u001e\u0010&\u001a\u00020\t2\u0006\u0010\'\u001a\u00020(2\u0006\u0010)\u001a\u00020\u00042\u0006\u0010*\u001a\u00020\u0004J\u0006\u0010+\u001a\u00020\tJ\u0006\u0010,\u001a\u00020\tJ\u001b\u0010-\u001a\u00020\u00122\u0006\u0010\u0005\u001a\u00020\u00062\u0006\u0010.\u001a\u00020\u0017\u00a2\u0006\u0002\u0010/J\u0006\u0010\b\u001a\u00020\tJ \u00100\u001a\u00020\t2\u0006\u00101\u001a\u0002022\u0006\u0010)\u001a\u00020\u00042\u0006\u0010*\u001a\u00020\u0004H\u0002J\u000e\u00103\u001a\u00020\t2\u0006\u00104\u001a\u000202J\u0006\u00105\u001a\u00020\u0012J\u0006\u00106\u001a\u00020\u0012J\u0006\u00107\u001a\u00020\u0012J\u0014\u00108\u001a\u00020\u00122\f\u00109\u001a\b\u0012\u0004\u0012\u00020\u00120\u0011J\u0006\u0010:\u001a\u00020$R\u000e\u0010\u0003\u001a\u00020\u0004X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0010\u0010\u0005\u001a\u00020\u0006X\u0082.\u00a2\u0006\u0004\n\u0002\u0010\u0007R\u000e\u0010\b\u001a\u00020\tX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\n\u001a\u00020\tX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000b\u001a\u00020\fX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\r\u001a\u00020\fX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000e\u001a\u00020\u000fX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0016\u0010\u0010\u001a\n\u0012\u0004\u0012\u00020\u0012\u0018\u00010\u0011X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0014\u0010\u0013\u001a\b\u0012\u0004\u0012\u00020\u00150\u0014X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0016\u001a\u00020\u0017X\u0082.\u00a2\u0006\u0002\n\u0000R\u0010\u0010\u0018\u001a\u0004\u0018\u00010\u0015X\u0082\u000e\u00a2\u0006\u0002\n\u0000\u00a8\u0006<"}, d2 = {"Lcom/touptek/measurerealize/utils/CenterCircleMeasureHelper;", "", "()V", "draggedPointIndex", "", "imageView", "error/NonExistentClass", "Lerror/NonExistentClass;", "isDraggingPoint", "", "isInitialized", "lastTouchX", "", "lastTouchY", "longPressStartTime", "", "measurementUpdateCallback", "Lkotlin/Function0;", "", "measurements", "Ljava/util/ArrayList;", "Lcom/touptek/measurerealize/utils/CenterCircleMeasurement;", "originalBitmap", "Landroid/graphics/Bitmap;", "selectedMeasurement", "addNewCenterCircleMeasurement", "clearAllMeasurements", "clearSelection", "deleteSelectedMeasurement", "getAllMeasurementData", "", "Lcom/touptek/measurerealize/utils/CenterCircleMeasurementData;", "getAllMeasurements", "getMeasurementCount", "getMeasurementData", "getMeasurementStats", "", "getSelectedMeasurement", "handleTouchEvent", "event", "Landroid/view/MotionEvent;", "viewWidth", "viewHeight", "hasMeasurements", "hasSelectedMeasurement", "init", "bitmap", "(Lerror/NonExistentClass;Landroid/graphics/Bitmap;)V", "isInImageContentArea", "point", "Landroid/graphics/PointF;", "isNearAnyMeasurement", "touchPoint", "onScaleChanged", "pauseMeasurement", "resumeMeasurement", "setMeasurementUpdateCallback", "callback", "startNewMeasurement", "Companion", "app_debug"})
public final class CenterCircleMeasureHelper {
    @org.jetbrains.annotations.NotNull
    public static final com.touptek.measurerealize.utils.CenterCircleMeasureHelper.Companion Companion = null;
    private static final java.lang.String TAG = "CenterCircleMeasureHelper";
    private static final float TOUCH_RADIUS = 80.0F;
    private static final float CLICK_TOLERANCE = 20.0F;
    private static final long LONG_PRESS_DURATION = 800L;
    private error.NonExistentClass imageView;
    private android.graphics.Bitmap originalBitmap;
    private boolean isInitialized = false;
    private final java.util.ArrayList<com.touptek.measurerealize.utils.CenterCircleMeasurement> measurements = null;
    private com.touptek.measurerealize.utils.CenterCircleMeasurement selectedMeasurement;
    private boolean isDraggingPoint = false;
    private int draggedPointIndex = -1;
    private long longPressStartTime = 0L;
    private float lastTouchX = 0.0F;
    private float lastTouchY = 0.0F;
    private kotlin.jvm.functions.Function0<kotlin.Unit> measurementUpdateCallback;
    
    public CenterCircleMeasureHelper() {
        super();
    }
    
    /**
     * 🔵 初始化助手
     */
    public final void init(@org.jetbrains.annotations.NotNull
    error.NonExistentClass imageView, @org.jetbrains.annotations.NotNull
    android.graphics.Bitmap bitmap) {
    }
    
    /**
     * 🔵 设置测量更新回调
     */
    public final void setMeasurementUpdateCallback(@org.jetbrains.annotations.NotNull
    kotlin.jvm.functions.Function0<kotlin.Unit> callback) {
    }
    
    /**
     * 🔵 创建新圆形测量
     */
    @org.jetbrains.annotations.NotNull
    public final java.lang.String startNewMeasurement() {
        return null;
    }
    
    /**
     * 🔵 添加新的圆形测量
     */
    public final boolean addNewCenterCircleMeasurement() {
        return false;
    }
    
    /**
     * 🔵 获取测量数量
     */
    public final int getMeasurementCount() {
        return 0;
    }
    
    /**
     * 🔵 检查是否有选中的测量
     */
    public final boolean hasSelectedMeasurement() {
        return false;
    }
    
    /**
     * 🔵 检查是否正在拖拽点
     */
    public final boolean isDraggingPoint() {
        return false;
    }
    
    /**
     * 🔵 检查是否靠近任何测量
     */
    public final boolean isNearAnyMeasurement(@org.jetbrains.annotations.NotNull
    android.graphics.PointF touchPoint) {
        return false;
    }
    
    /**
     * 🔵 获取所有测量数据
     */
    @org.jetbrains.annotations.NotNull
    public final java.util.List<com.touptek.measurerealize.utils.CenterCircleMeasurement> getAllMeasurements() {
        return null;
    }
    
    /**
     * 🔵 清除所有选中状态
     */
    public final void clearSelection() {
    }
    
    /**
     * 🔵 处理触摸事件 - 核心交互逻辑
     */
    public final boolean handleTouchEvent(@org.jetbrains.annotations.NotNull
    android.view.MotionEvent event, int viewWidth, int viewHeight) {
        return false;
    }
    
    /**
     * 🔵 检查点是否在图像内容区域
     */
    private final boolean isInImageContentArea(android.graphics.PointF point, int viewWidth, int viewHeight) {
        return false;
    }
    
    /**
     * 🔵 删除选中的测量
     */
    public final boolean deleteSelectedMeasurement() {
        return false;
    }
    
    /**
     * 🔵 删除所有测量
     */
    public final void clearAllMeasurements() {
    }
    
    /**
     * 🔵 缩放变化时同步坐标
     */
    public final void onScaleChanged() {
    }
    
    /**
     * 🔵 暂停测量（保留数据）
     */
    public final void pauseMeasurement() {
    }
    
    /**
     * 🔵 恢复测量
     */
    public final void resumeMeasurement() {
    }
    
    /**
     * 🔵 获取选中的测量
     */
    @org.jetbrains.annotations.Nullable
    public final com.touptek.measurerealize.utils.CenterCircleMeasurement getSelectedMeasurement() {
        return null;
    }
    
    /**
     * 🔵 检查是否有测量数据
     */
    public final boolean hasMeasurements() {
        return false;
    }
    
    /**
     * 🔵 获取测量统计信息
     */
    @org.jetbrains.annotations.NotNull
    public final java.lang.String getMeasurementStats() {
        return null;
    }
    
    /**
     * 🔵 获取所有测量数据用于覆盖层显示
     */
    @org.jetbrains.annotations.NotNull
    public final java.util.List<com.touptek.measurerealize.utils.CenterCircleMeasurementData> getAllMeasurementData() {
        return null;
    }
    
    /**
     * 🔵 获取当前选中的测量数据
     */
    @org.jetbrains.annotations.Nullable
    public final com.touptek.measurerealize.utils.CenterCircleMeasurementData getMeasurementData() {
        return null;
    }
    
    @kotlin.Metadata(mv = {1, 7, 1}, k = 1, d1 = {"\u0000 \n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\u0007\n\u0000\n\u0002\u0010\t\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0002\b\u0086\u0003\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002R\u000e\u0010\u0003\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0005\u001a\u00020\u0006X\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0007\u001a\u00020\bX\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\t\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000\u00a8\u0006\n"}, d2 = {"Lcom/touptek/measurerealize/utils/CenterCircleMeasureHelper$Companion;", "", "()V", "CLICK_TOLERANCE", "", "LONG_PRESS_DURATION", "", "TAG", "", "TOUCH_RADIUS", "app_debug"})
    public static final class Companion {
        
        private Companion() {
            super();
        }
    }
}