package com.touptek.measurerealize

import android.content.Context
import android.graphics.Bitmap
import android.util.Log
import android.view.MotionEvent
import android.view.View
import android.widget.TextView
import com.touptek.measurerealize.utils.AngleMeasureHelper
import com.touptek.measurerealize.utils.FourPointAngleHelper
import com.touptek.measurerealize.utils.MeasurementOverlayView
import com.touptek.measurerealize.TpImageView
import com.touptek.measurerealize.utils.EllipseMeasureHelper
import com.touptek.measurerealize.utils.PointMeasureHelper
import com.touptek.measurerealize.utils.LineMeasureHelper
import com.touptek.measurerealize.utils.HorizonLineMeasureHelper
import com.touptek.measurerealize.utils.VerticalLineMeasureHelper
import com.touptek.measurerealize.utils.ParallelLinesMeasureHelper
import com.touptek.measurerealize.utils.ThreeVerticalMeasureHelper
import com.touptek.measurerealize.utils.RectangleMeasureHelper
import com.touptek.measurerealize.utils.ThreeRectangleMeasureHelper
import kotlin.math.cos

/**
 * 🎨 专业级测量管理器 - 高度封装的测量功能管理类
 * 
 * 核心职责：
 * 1. 管理测量状态和生命周期
 * 2. 封装TpImageView与测量功能的集成
 * 3. 提供极简的API接口给上层调用
 * 4. 处理所有复杂的交互逻辑
 * 
 * 设计理念：让上层调用者只需要关心开始/停止测量，所有细节都由Manager处理
 */
class MeasurementManager(
    private val context: Context,
    private val imageView: TpImageView,
    private val overlayView: MeasurementOverlayView,
    private val statusTextView: TextView? = null
) {
    
    companion object {
        private const val TAG = "MeasurementManager"
    }

    /**
     * 🔒 选中状态保存数据类 - 用于删除操作保护
     */
    private data class SelectionState(
        val activeMeasurementMode: MeasurementMode?,
        val angleSelectedId: String?,
        val fourPointAngleSelectedId: String?,
        val pointSelectedId: String?,
        val lineSelectedId: String?,
        val horizonLineSelectedId: String?,
        val verticalLineSelectedId: String?,
        val parallelLinesSelectedId: String?,
        val threeVerticalSelectedId: String?
    )
    
    // 核心组件
    private val angleMeasureHelper = AngleMeasureHelper()
    private val fourPointAngleHelper = FourPointAngleHelper()
    private val pointMeasureHelper = PointMeasureHelper()
    private val lineMeasureHelper = LineMeasureHelper()
    private val horizonLineMeasureHelper = HorizonLineMeasureHelper()
    private val verticalLineMeasureHelper = VerticalLineMeasureHelper()
    private val parallelLinesMeasureHelper = ParallelLinesMeasureHelper()
    private val threeVerticalMeasureHelper = ThreeVerticalMeasureHelper()
    private val rectangleMeasureHelper = RectangleMeasureHelper()
    private val threeRectangleMeasureHelper = ThreeRectangleMeasureHelper()
    private val ellipseMeasureHelper = EllipseMeasureHelper()
    private val touchHandler = MeasurementTouchHandler()

    /**
     * 测量模式枚举
     */
    enum class MeasurementMode {
        ANGLE,           // 三点角度测量
        FOUR_POINT_ANGLE, // 四点角度测量
        POINT,           // 点测量
        LINE,            // 线段测量
        HORIZON_LINE,    // 水平线测量
        VERTICAL_LINE,   // 垂直线测量
        PARALLEL_LINES,  // 平行线测量
        THREE_VERTICAL,  // 三垂直测量
        RECTANGLE,       // 矩形测量
        THREE_RECTANGLE, // 三点矩形测量
        ELLIPSE          // 椭圆测量
    }

    // 状态管理
    private var isAngleMeasuring = false
    private var isFourPointAngleMeasuring = false
    private var isPointMeasuring = false
    private var isLineMeasuring = false
    private var isHorizonLineMeasuring = false
    private var isVerticalLineMeasuring = false
    private var isParallelLinesMeasuring = false
    private var isThreeVerticalMeasuring = false
    private var isRectangleMeasuring = false
    private var isThreeRectangleMeasuring = false
    private var isEllipseMeasuring = false
    private var activeMeasurementMode: MeasurementMode? = null // 当前激活的测量模式
    private var currentBitmap: Bitmap? = null
    private var isInitialized = false
    
    /**
     * 🚀 初始化测量管理器
     */
    fun initialize(bitmap: Bitmap) {
        try {
            Log.d(TAG, "🚀 Initializing MeasurementManager")
            
            currentBitmap = bitmap.copy(Bitmap.Config.ARGB_8888, true)
            
            // 设置覆盖层的ImageView引用
            overlayView.setImageView(imageView)

            // 设置覆盖层的AngleMeasureHelper引用
            overlayView.setAngleMeasureHelper(angleMeasureHelper)

            // 设置覆盖层的FourPointAngleHelper引用
            overlayView.setFourPointAngleHelper(fourPointAngleHelper)

            // 设置覆盖层的PointMeasureHelper引用
            overlayView.setPointMeasureHelper(pointMeasureHelper)

            // 设置覆盖层的LineMeasureHelper引用
            overlayView.setLineMeasureHelper(lineMeasureHelper)

            // 设置覆盖层的HorizonLineMeasureHelper引用
            overlayView.setHorizonLineMeasureHelper(horizonLineMeasureHelper)

            // 设置覆盖层的VerticalLineMeasureHelper引用
            overlayView.setVerticalLineMeasureHelper(verticalLineMeasureHelper)

            // 设置覆盖层的ParallelLinesMeasureHelper引用
            overlayView.setParallelLinesMeasureHelper(parallelLinesMeasureHelper)

            // 设置覆盖层的ThreeVerticalMeasureHelper引用
            overlayView.setThreeVerticalMeasureHelper(threeVerticalMeasureHelper)

            // 设置覆盖层的RectangleMeasureHelper引用
            overlayView.setRectangleMeasureHelper(rectangleMeasureHelper)

            // 设置覆盖层的ThreeRectangleMeasureHelper引用
            overlayView.setThreeRectangleMeasureHelper(threeRectangleMeasureHelper)

            // 初始化触摸处理器
            touchHandler.initialize(imageView, angleMeasureHelper)
            
            isInitialized = true
            Log.d(TAG, "✅ MeasurementManager initialized successfully")
            
        } catch (e: Exception) {
            Log.e(TAG, "❌ Failed to initialize MeasurementManager: ${e.message}")
            e.printStackTrace()
        }
    }
    
    /**
     * 🎯 开始角度测量 - 支持混合模式
     */
    fun startAngleMeasurement(): Boolean {
        if (!isInitialized) {
            Log.e(TAG, "❌ MeasurementManager not initialized")
            return false
        }

        if (isAngleMeasuring) {
            Log.w(TAG, "⚠️ Angle measurement already in progress")
            return true
        }

        // 🔄 混合共存模式：如果四点角度测量存在，将其设为未激活状态
        if (isFourPointAngleMeasuring) {
            Log.d(TAG, "🔄 Deactivating four-point angle measurement, keeping data")
            // 不需要调用 deactivateFourPointAngleMeasurement()，直接设置新的激活模式
        }

        try {
            Log.d(TAG, "🎯 Starting angle measurement - mixed coexistence mode")

            // 1. 设置混合触摸监听器
            setupMixedTouchHandler()

            currentBitmap?.let { bitmap ->
                // 2. 🚀 初始化专业级角度测量助手
                val textView = statusTextView ?: TextView(context)
                angleMeasureHelper.init(imageView, textView, bitmap.copy(Bitmap.Config.ARGB_8888, true))

                // 3. 设置测量更新回调
                angleMeasureHelper.setMeasurementUpdateCallback {
                    updateMixedOverlayDisplay()
                }

                // 4. 🎨 设置统一缩放监听器
                setupUnifiedScaleListener()

                // 5. 🚀 设置测量状态和激活模式
                isAngleMeasuring = true
                activeMeasurementMode = MeasurementMode.ANGLE
                Log.d(TAG, "🚀 [DEBUG] Set isAngleMeasuring = true, activeMeasurementMode = ANGLE")

                // 6. 🎯 直接在屏幕中心生成可拖动的角度
                val measurementId = angleMeasureHelper.startNewMeasurement()
                Log.d(TAG, "🎯 Generated draggable angle at screen center: $measurementId")

                // 7. 🔄 立即更新混合覆盖层显示 - 确保数据同步
                updateMixedOverlayDisplay()

                // 8. 🎯 强制显示机制
                forceOverlayDisplay()

                // 9. 🔄 延迟再次强制显示确保时序
                overlayView.post {
                    forceOverlayDisplay()
                }

                // 10. 更新UI提示
                statusTextView?.apply {
                    visibility = View.VISIBLE
                    text = "角度已生成！拖动任意点调整角度，长按删除"
                }

                Log.d(TAG, "✅ Angle measurement started successfully with enhanced display forcing")
                return true
            }

            Log.e(TAG, "❌ No bitmap available for measurement")
            return false

        } catch (e: Exception) {
            Log.e(TAG, "❌ Failed to start angle measurement: ${e.message}")
            e.printStackTrace()
            return false
        }
    }
    
    /**
     * ⏹️ 停止角度测量 - 极简API
     */
    fun stopAngleMeasurement() {
        if (!isAngleMeasuring) {
            Log.w(TAG, "⚠️ No angle measurement in progress")
            return
        }
        
        try {
            Log.d(TAG, "⏹️ Stopping angle measurement")
            
            // 1. 暂停测量助手（保留数据）
            angleMeasureHelper.pauseMeasurement()
            
            // 2. 恢复TpImageView的原始触摸处理
            imageView.restoreTouchListener()
            
            // 3. 隐藏覆盖层
            overlayView.visibility = View.GONE
            
            // 4. 更新状态
            isAngleMeasuring = false
            
            // 5. 隐藏状态文本
            statusTextView?.visibility = View.GONE
            
            Log.d(TAG, "✅ Angle measurement stopped - data preserved")
            
        } catch (e: Exception) {
            Log.e(TAG, "❌ Failed to stop angle measurement: ${e.message}")
            e.printStackTrace()
        }
    }

    /**
     * 🎯 开始四点角度测量 - 支持混合模式
     */
    fun startFourPointAngleMeasurement(): Boolean {
        if (!isInitialized) {
            Log.e(TAG, "❌ MeasurementManager not initialized")
            return false
        }

        if (isFourPointAngleMeasuring) {
            Log.w(TAG, "⚠️ Four-point angle measurement already in progress")
            return true
        }

        // 🔄 混合共存模式：如果三点角度测量存在，将其设为未激活状态
        if (isAngleMeasuring) {
            Log.d(TAG, "🔄 Deactivating angle measurement, keeping data")
            // 不需要调用 deactivateAngleMeasurement()，直接设置新的激活模式
        }

        try {
            Log.d(TAG, "🎯 Starting four-point angle measurement - mixed coexistence mode")

            // 1. 设置混合触摸监听器
            setupMixedTouchHandler()

            currentBitmap?.let { bitmap ->
                // 2. 🚀 初始化四点角度测量助手
                fourPointAngleHelper.init(imageView, bitmap.copy(Bitmap.Config.ARGB_8888, true))

                // 3. 设置测量更新回调
                fourPointAngleHelper.setMeasurementUpdateCallback {
                    updateMixedOverlayDisplay()
                }

                // 4. 🎨 设置统一缩放监听器
                setupUnifiedScaleListener()

                // 5. 🚀 设置测量状态和激活模式
                isFourPointAngleMeasuring = true
                activeMeasurementMode = MeasurementMode.FOUR_POINT_ANGLE
                Log.d(TAG, "🚀 [DEBUG] Set isFourPointAngleMeasuring = true, activeMeasurementMode = FOUR_POINT_ANGLE")

                // 6. 🎯 直接在屏幕中心生成可拖动的四点角度
                val measurementId = fourPointAngleHelper.startNewMeasurement()
                Log.d(TAG, "🎯 Generated draggable four-point angle at screen center: $measurementId")

                // 7. 🔄 立即更新混合覆盖层显示
                updateMixedOverlayDisplay()

                // 8. 🎯 强制显示机制
                forceOverlayDisplay()

                // 9. 🔄 延迟再次强制显示确保时序
                overlayView.post {
                    forceOverlayDisplay()
                }

                // 10. 更新UI提示
                statusTextView?.apply {
                    visibility = View.VISIBLE
                    text = "四点角度已生成！拖动任意点调整角度，长按删除"
                }

                Log.d(TAG, "✅ Four-point angle measurement started successfully")
                return true
            }

            Log.e(TAG, "❌ No bitmap available for four-point measurement")
            return false

        } catch (e: Exception) {
            Log.e(TAG, "❌ Failed to start four-point angle measurement: ${e.message}")
            e.printStackTrace()
            return false
        }
    }

    /**
     * ⏹️ 停止四点角度测量
     */
    fun stopFourPointAngleMeasurement() {
        if (!isFourPointAngleMeasuring) {
            Log.w(TAG, "⚠️ No four-point angle measurement in progress")
            return
        }

        try {
            Log.d(TAG, "⏹️ Stopping four-point angle measurement")

            // 1. 暂停测量助手（保留数据）
            fourPointAngleHelper.pauseMeasurement()

            // 2. 恢复TpImageView的原始触摸处理
            imageView.restoreTouchListener()

            // 3. 隐藏覆盖层
            overlayView.visibility = View.GONE

            // 4. 更新状态
            isFourPointAngleMeasuring = false

            // 5. 隐藏状态文本
            statusTextView?.visibility = View.GONE

            Log.d(TAG, "✅ Four-point angle measurement stopped - data preserved")

        } catch (e: Exception) {
            Log.e(TAG, "❌ Failed to stop four-point angle measurement: ${e.message}")
            e.printStackTrace()
        }
    }

    /**
     * 🎯 开始点测量 - 支持混合模式
     */
    fun startPointMeasurement(): Boolean {
        if (!isInitialized) {
            Log.e(TAG, "❌ MeasurementManager not initialized")
            return false
        }

        val bitmap = currentBitmap
        if (bitmap == null) {
            Log.e(TAG, "❌ No bitmap available for point measurement")
            return false
        }

        try {
            Log.d(TAG, "🎯 Starting point measurement")

            // 1. 初始化点测量助手
            pointMeasureHelper.init(imageView, bitmap)

            // 2. 设置测量更新回调
            pointMeasureHelper.setMeasurementUpdateCallback {
                updatePointMeasurementDisplay()
            }

            // 3. 在屏幕中心生成默认点
            val measurementId = pointMeasureHelper.startNewMeasurement()

            // 4. 设置混合模式触摸处理
            setupMixedTouchHandler()

            // 5. 🎨 设置统一缩放监听器
            setupUnifiedScaleListener()

            // 6. 强制显示覆盖层
            forceOverlayDisplay()

            // 7. 更新状态
            isPointMeasuring = true
            activeMeasurementMode = MeasurementMode.POINT

            // 8. 立即更新显示
            updatePointMeasurementDisplay()

            Log.d(TAG, "✅ Point measurement started successfully - ID: $measurementId")
            return true

        } catch (e: Exception) {
            Log.e(TAG, "❌ Failed to start point measurement: ${e.message}")
            e.printStackTrace()
            return false
        }
    }

    /**
     * 🛑 停止点测量
     */
    fun stopPointMeasurement() {
        if (!isPointMeasuring) {
            Log.d(TAG, "⚠️ Point measurement not active")
            return
        }

        try {
            Log.d(TAG, "⏹️ Stopping point measurement")

            // 1. 暂停测量助手（保留数据）
            pointMeasureHelper.pauseMeasurement()

            // 2. 恢复TpImageView的原始触摸处理
            imageView.restoreTouchListener()

            // 3. 隐藏覆盖层
            overlayView.visibility = View.GONE

            // 4. 更新状态
            isPointMeasuring = false

            // 5. 隐藏状态文本
            statusTextView?.visibility = View.GONE

            Log.d(TAG, "✅ Point measurement stopped - data preserved")

        } catch (e: Exception) {
            Log.e(TAG, "❌ Failed to stop point measurement: ${e.message}")
            e.printStackTrace()
        }
    }

    /**
     * 📏 开始线段测量 - 极简API
     */
    fun startLineMeasurement(): Boolean {
        if (!isInitialized) {
            Log.e(TAG, "❌ MeasurementManager not initialized")
            return false
        }

        if (isLineMeasuring) {
            Log.w(TAG, "⚠️ Line measurement already in progress")
            return true
        }

        val bitmap = currentBitmap ?: run {
            Log.e(TAG, "❌ No bitmap available for line measurement")
            return false
        }

        try {
            Log.d(TAG, "📏 Starting line measurement")

            // 1. 初始化线段测量助手
            lineMeasureHelper.init(imageView, bitmap)

            // 2. 设置测量更新回调
            lineMeasureHelper.setMeasurementUpdateCallback {
                updateLineMeasurementDisplay()
            }

            // 3. 在屏幕中心生成默认线段
            val measurementId = lineMeasureHelper.startNewMeasurement()

            // 4. 设置混合模式触摸处理
            setupMixedTouchHandler()

            // 5. 🎨 设置统一缩放监听器
            setupUnifiedScaleListener()

            // 6. 强制显示覆盖层
            forceOverlayDisplay()

            // 7. 更新状态
            isLineMeasuring = true
            activeMeasurementMode = MeasurementMode.LINE

            // 8. 立即更新显示
            updateLineMeasurementDisplay()

            Log.d(TAG, "✅ Line measurement started successfully - ID: $measurementId")
            return true

        } catch (e: Exception) {
            Log.e(TAG, "❌ Failed to start line measurement: ${e.message}")
            e.printStackTrace()
            return false
        }
    }

    /**
     * 🛑 停止线段测量
     */
    fun stopLineMeasurement() {
        if (!isLineMeasuring) {
            Log.d(TAG, "⚠️ Line measurement not active")
            return
        }

        try {
            Log.d(TAG, "🛑 Stopping line measurement")

            // 1. 清除选中状态
            lineMeasureHelper.clearAllSelections()

            // 2. 隐藏覆盖层
            overlayView.visibility = View.GONE

            // 3. 更新状态
            isLineMeasuring = false

            // 4. 隐藏状态文本
            statusTextView?.visibility = View.GONE

            Log.d(TAG, "✅ Line measurement stopped - data preserved")

        } catch (e: Exception) {
            Log.e(TAG, "❌ Failed to stop line measurement: ${e.message}")
            e.printStackTrace()
        }
    }

    /**
     * 📏 开始水平线测量 - 极简API
     */
    fun startHorizonLineMeasurement(): Boolean {
        if (!isInitialized) {
            Log.e(TAG, "❌ MeasurementManager not initialized")
            return false
        }

        if (isHorizonLineMeasuring) {
            Log.w(TAG, "⚠️ Horizon line measurement already in progress")
            return true
        }

        val bitmap = currentBitmap ?: run {
            Log.e(TAG, "❌ No bitmap available for horizon line measurement")
            return false
        }

        try {
            Log.d(TAG, "📏 Starting horizon line measurement")

            // 1. 初始化水平线测量助手
            horizonLineMeasureHelper.init(imageView, bitmap)

            // 2. 设置测量更新回调
            horizonLineMeasureHelper.setMeasurementUpdateCallback {
                updateHorizonLineMeasurementDisplay()
            }

            // 3. 在屏幕中心生成默认水平线
            val measurementId = horizonLineMeasureHelper.startNewMeasurement()

            // 4. 设置混合模式触摸处理
            setupMixedTouchHandler()

            // 5. 🎨 设置统一缩放监听器
            setupUnifiedScaleListener()

            // 6. 强制显示覆盖层
            forceOverlayDisplay()

            // 7. 更新状态
            isHorizonLineMeasuring = true
            activeMeasurementMode = MeasurementMode.HORIZON_LINE

            // 8. 立即更新显示
            updateHorizonLineMeasurementDisplay()

            Log.d(TAG, "✅ Horizon line measurement started successfully - ID: $measurementId")
            return true

        } catch (e: Exception) {
            Log.e(TAG, "❌ Failed to start horizon line measurement: ${e.message}")
            e.printStackTrace()
            return false
        }
    }

    /**
     * 📏 开始垂直线测量 - 极简API
     */
    fun startVerticalLineMeasurement(): Boolean {
        if (!isInitialized) {
            Log.e(TAG, "❌ MeasurementManager not initialized")
            return false
        }

        if (isVerticalLineMeasuring) {
            Log.w(TAG, "⚠️ Vertical line measurement already in progress")
            return true
        }

        val bitmap = currentBitmap ?: run {
            Log.e(TAG, "❌ No bitmap available for vertical line measurement")
            return false
        }

        try {
            Log.d(TAG, "📏 Starting vertical line measurement")

            // 1. 初始化垂直线测量助手
            verticalLineMeasureHelper.init(imageView, bitmap)

            // 2. 设置测量更新回调
            verticalLineMeasureHelper.setMeasurementUpdateCallback {
                updateVerticalLineMeasurementDisplay()
            }

            // 3. 在屏幕中心生成默认垂直线
            val measurementId = verticalLineMeasureHelper.startNewMeasurement()

            // 4. 设置混合模式触摸处理
            setupMixedTouchHandler()

            // 5. 🎨 设置统一缩放监听器
            setupUnifiedScaleListener()

            // 6. 强制显示覆盖层
            forceOverlayDisplay()

            // 7. 更新状态
            isVerticalLineMeasuring = true
            activeMeasurementMode = MeasurementMode.VERTICAL_LINE

            // 8. 立即更新显示
            updateVerticalLineMeasurementDisplay()

            Log.d(TAG, "✅ Vertical line measurement started successfully - ID: $measurementId")
            return true

        } catch (e: Exception) {
            Log.e(TAG, "❌ Failed to start vertical line measurement: ${e.message}")
            e.printStackTrace()
            return false
        }
    }

    /**
     * 🛑 停止水平线测量
     */
    fun stopHorizonLineMeasurement() {
        if (!isHorizonLineMeasuring) {
            Log.d(TAG, "⚠️ Horizon line measurement not active")
            return
        }

        try {
            Log.d(TAG, "🛑 Stopping horizon line measurement")

            // 1. 清除选中状态
            horizonLineMeasureHelper.clearAllSelections()

            // 2. 隐藏覆盖层
            overlayView.visibility = View.GONE

            // 3. 更新状态
            isHorizonLineMeasuring = false

            // 4. 隐藏状态文本
            statusTextView?.visibility = View.GONE

            Log.d(TAG, "✅ Horizon line measurement stopped - data preserved")

        } catch (e: Exception) {
            Log.e(TAG, "❌ Failed to stop horizon line measurement: ${e.message}")
            e.printStackTrace()
        }
    }

    /**
     * 🛑 停止垂直线测量
     */
    fun stopVerticalLineMeasurement() {
        if (!isVerticalLineMeasuring) {
            Log.d(TAG, "⚠️ Vertical line measurement not active")
            return
        }

        try {
            Log.d(TAG, "🛑 Stopping vertical line measurement")

            // 1. 清除选中状态
            verticalLineMeasureHelper.clearAllSelections()

            // 2. 隐藏覆盖层
            overlayView.visibility = View.GONE

            // 3. 更新状态
            isVerticalLineMeasuring = false

            // 4. 隐藏状态文本
            statusTextView?.visibility = View.GONE

            Log.d(TAG, "✅ Vertical line measurement stopped - data preserved")

        } catch (e: Exception) {
            Log.e(TAG, "❌ Failed to stop vertical line measurement: ${e.message}")
            e.printStackTrace()
        }
    }

    /**
     * 📏 开始平行线测量 - 极简API
     */
    fun startParallelLinesMeasurement(): Boolean {
        if (!isInitialized) {
            Log.e(TAG, "❌ MeasurementManager not initialized")
            return false
        }

        if (isParallelLinesMeasuring) {
            Log.w(TAG, "⚠️ Parallel lines measurement already in progress")
            return true
        }

        val bitmap = currentBitmap ?: run {
            Log.e(TAG, "❌ No bitmap available for parallel lines measurement")
            return false
        }

        try {
            Log.d(TAG, "📏 Starting parallel lines measurement")

            // 1. 初始化平行线测量助手
            parallelLinesMeasureHelper.init(imageView, bitmap)

            // 2. 🔧 先设置状态，避免回调时状态不正确
            isParallelLinesMeasuring = true
            activeMeasurementMode = MeasurementMode.PARALLEL_LINES

            // 3. 设置测量更新回调
            parallelLinesMeasureHelper.setMeasurementUpdateCallback {
                updateParallelLinesMeasurementDisplay()
            }

            // 4. 在屏幕中心生成默认平行线
            val measurementId = parallelLinesMeasureHelper.startNewMeasurement()

            // 5. 验证测量创建和选中状态
            val hasSelected = parallelLinesMeasureHelper.hasSelectedMeasurement()
            val measurementCount = parallelLinesMeasureHelper.getMeasurementCount()
            Log.d(TAG, "🔍 After measurement creation - count: $measurementCount, hasSelected: $hasSelected")

            // 6. 🔧 强制确保选中状态正确（修复选中状态丢失问题）
            if (measurementCount > 0 && !hasSelected) {
                Log.w(TAG, "⚠️ Selection state lost after creation, forcing selection of last measurement")
                val measurements = parallelLinesMeasureHelper.getAllMeasurements()
                measurements.lastOrNull()?.let { lastMeasurement ->
                    // 清除其他测量的选中状态
                    measurements.forEach { it.isSelected = false }
                    // 选中最后创建的测量
                    lastMeasurement.isSelected = true
                    parallelLinesMeasureHelper.setSelectedMeasurement(lastMeasurement)
                    Log.d(TAG, "🔧 Forced selection of measurement: ${lastMeasurement.id}")

                    // 验证修复结果
                    val fixedHasSelected = parallelLinesMeasureHelper.hasSelectedMeasurement()
                    Log.d(TAG, "🔍 After forced selection - hasSelected: $fixedHasSelected")
                }
            }

            // 6. 设置混合模式触摸处理
            setupMixedTouchHandler()

            // 7. 显示覆盖层
            overlayView.visibility = View.VISIBLE

            // 8. 立即更新显示
            updateParallelLinesMeasurementDisplay()
            forceOverlayDisplay()

            // 9. 延迟再次强制显示确保时序
            overlayView.post {
                forceOverlayDisplay()
            }

            Log.d(TAG, "✅ Parallel lines measurement started successfully: $measurementId")
            return true

        } catch (e: Exception) {
            Log.e(TAG, "❌ Failed to start parallel lines measurement: ${e.message}")
            e.printStackTrace()
            return false
        }
    }

    /**
     * 🛑 停止平行线测量
     */
    fun stopParallelLinesMeasurement() {
        if (!isParallelLinesMeasuring) {
            Log.d(TAG, "⚠️ Parallel lines measurement not active")
            return
        }

        try {
            Log.d(TAG, "🛑 Stopping parallel lines measurement")

            // 1. 清除选中状态
            parallelLinesMeasureHelper.clearAllMeasurements()

            // 2. 隐藏覆盖层
            overlayView.visibility = View.GONE

            // 3. 更新状态
            isParallelLinesMeasuring = false

            // 4. 隐藏状态文本
            statusTextView?.visibility = View.GONE

            Log.d(TAG, "✅ Parallel lines measurement stopped - data preserved")

        } catch (e: Exception) {
            Log.e(TAG, "❌ Failed to stop parallel lines measurement: ${e.message}")
            e.printStackTrace()
        }
    }

    /**
     * 📏 开始三垂直测量 - 极简API
     */
    fun startThreeVerticalMeasurement(): Boolean {
        if (!isInitialized) {
            Log.e(TAG, "❌ MeasurementManager not initialized")
            return false
        }

        if (isThreeVerticalMeasuring) {
            Log.w(TAG, "⚠️ Three vertical measurement already in progress")
            return true
        }

        val bitmap = currentBitmap ?: run {
            Log.e(TAG, "❌ No bitmap available for three vertical measurement")
            return false
        }

        try {
            Log.d(TAG, "📏 Starting three vertical measurement")

            // 1. 初始化三垂直测量助手
            threeVerticalMeasureHelper.init(imageView, bitmap)

            // 2. 🔧 先设置状态，避免回调时状态不正确
            isThreeVerticalMeasuring = true
            activeMeasurementMode = MeasurementMode.THREE_VERTICAL

            // 3. 设置测量更新回调
            threeVerticalMeasureHelper.setMeasurementUpdateCallback {
                updateThreeVerticalMeasurementDisplay()
            }

            // 4. 在屏幕中心生成默认三垂直测量
            val measurementId = threeVerticalMeasureHelper.startNewMeasurement()

            // 5. 验证测量创建和选中状态
            val hasSelected = threeVerticalMeasureHelper.hasSelectedMeasurement()
            val measurementCount = threeVerticalMeasureHelper.getMeasurementCount()
            Log.d(TAG, "🔍 After measurement creation - count: $measurementCount, hasSelected: $hasSelected")

            // 6. 🔧 强制确保选中状态正确（修复选中状态丢失问题）
            if (measurementCount > 0 && !hasSelected) {
                Log.w(TAG, "⚠️ Selection state lost after creation, forcing selection of last measurement")
                val measurements = threeVerticalMeasureHelper.getAllMeasurements()
                measurements.lastOrNull()?.let { lastMeasurement ->
                    // 清除其他测量的选中状态
                    measurements.forEach { it.isSelected = false }
                    // 选中最后创建的测量
                    lastMeasurement.isSelected = true
                    threeVerticalMeasureHelper.setSelectedMeasurement(lastMeasurement)
                    Log.d(TAG, "🔧 Forced selection of measurement: ${lastMeasurement.id}")

                    // 验证修复结果
                    val fixedHasSelected = threeVerticalMeasureHelper.hasSelectedMeasurement()
                    Log.d(TAG, "🔍 After forced selection - hasSelected: $fixedHasSelected")
                }
            }

            // 6. 设置混合模式触摸处理
            setupMixedTouchHandler()

            // 7. 设置统一缩放监听器
            setupUnifiedScaleListener()

            // 8. 强制显示覆盖层
            forceOverlayDisplay()

            // 9. 立即更新显示
            updateThreeVerticalMeasurementDisplay()

            Log.d(TAG, "✅ Three vertical measurement started successfully - ID: $measurementId")
            return true

        } catch (e: Exception) {
            Log.e(TAG, "❌ Failed to start three vertical measurement: ${e.message}")
            e.printStackTrace()
            return false
        }
    }

    /**
     * 📦 开始矩形测量 - 极简API
     */
    fun startRectangleMeasurement(): Boolean {
        if (!isInitialized) {
            Log.e(TAG, "❌ MeasurementManager not initialized")
            return false
        }

        if (isRectangleMeasuring) {
            Log.w(TAG, "⚠️ Rectangle measurement already in progress")
            return true
        }

        val bitmap = currentBitmap ?: run {
            Log.e(TAG, "❌ No bitmap available for rectangle measurement")
            return false
        }

        try {
            Log.d(TAG, "📦 Starting rectangle measurement")

            // 1. 初始化矩形测量助手
            rectangleMeasureHelper.init(imageView, bitmap)

            // 2. 🔧 先设置状态，避免回调时状态不正确
            isRectangleMeasuring = true
            activeMeasurementMode = MeasurementMode.RECTANGLE

            // 3. 设置测量更新回调
            rectangleMeasureHelper.setMeasurementUpdateCallback {
                updateRectangleMeasurementDisplay()
            }

            // 4. 在屏幕中心生成默认矩形测量
            val measurementId = rectangleMeasureHelper.startNewMeasurement()

            // 5. 验证测量创建和选中状态
            val hasSelected = rectangleMeasureHelper.hasSelectedMeasurement()
            val measurementCount = rectangleMeasureHelper.getMeasurementCount()
            Log.d(TAG, "🔍 After rectangle creation - count: $measurementCount, hasSelected: $hasSelected")

            // 6. 设置混合模式触摸处理
            setupMixedTouchHandler()

            // 7. 设置统一缩放监听器
            setupUnifiedScaleListener()

            // 8. 强制显示覆盖层
            forceOverlayDisplay()

            // 9. 立即更新显示
            updateRectangleMeasurementDisplay()

            Log.d(TAG, "✅ Rectangle measurement started successfully - ID: $measurementId")
            return true

        } catch (e: Exception) {
            Log.e(TAG, "❌ Failed to start rectangle measurement: ${e.message}")
            e.printStackTrace()
            return false
        }
    }

    /**
     * 📦 添加新的矩形测量
     */
    fun addNewRectangleMeasurement(): String {
        if (!isRectangleMeasuring) {
            Log.w(TAG, "⚠️ Rectangle measurement not active")
            return ""
        }

        try {
            val measurementId = rectangleMeasureHelper.addNewMeasurement()
            updateRectangleMeasurementDisplay()
            Log.d(TAG, "✅ New rectangle measurement added: $measurementId")
            return measurementId
        } catch (e: Exception) {
            Log.e(TAG, "❌ Failed to add new rectangle measurement: ${e.message}")
            return ""
        }
    }

    /**
     * 🔵 开始椭圆测量 - 极简API
     */
    fun startEllipseMeasurement(): Boolean {
        if (!isInitialized) {
            Log.e(TAG, "❌ MeasurementManager not initialized")
            return false
        }

        if (isEllipseMeasuring) {
            Log.w(TAG, "⚠️ Ellipse measurement already in progress")
            return true
        }

        val bitmap = currentBitmap ?: run {
            Log.e(TAG, "❌ No bitmap available for ellipse measurement")
            return false
        }

        try {
            Log.d(TAG, "🔵 Starting ellipse measurement")

            // 1. 初始化椭圆测量助手
            ellipseMeasureHelper.init(imageView, bitmap)

            // 2. 设置状态
            isEllipseMeasuring = true
            activeMeasurementMode = MeasurementMode.ELLIPSE

            // 3. 设置测量更新回调
            ellipseMeasureHelper.setMeasurementUpdateCallback {
                updateEllipseMeasurementDisplay()
            }

            // 4. 在屏幕中心生成默认椭圆测量
            val measurementId = ellipseMeasureHelper.startNewMeasurement()

            // 5. 验证测量创建和选中状态
            val hasSelected = ellipseMeasureHelper.hasSelectedMeasurement()
            val measurementCount = ellipseMeasureHelper.getMeasurementCount()
            Log.d(TAG, "🔍 After ellipse creation - count: $measurementCount, hasSelected: $hasSelected")

            // 6. 设置混合模式触摸处理
            setupMixedTouchHandler()

            // 7. 设置统一缩放监听器
            setupUnifiedScaleListener()

            // 8. 强制显示覆盖层
            forceOverlayDisplay()

            // 9. 立即更新显示
            updateEllipseMeasurementDisplay()

            Log.d(TAG, "✅ Ellipse measurement started successfully - ID: $measurementId")
            return true

        } catch (e: Exception) {
            Log.e(TAG, "❌ Failed to start ellipse measurement: ${e.message}")
            e.printStackTrace()
            isEllipseMeasuring = false
            return false
        }
    }

    /**
     * 🔵 添加新的椭圆测量
     */
    fun addNewEllipseMeasurement(): Boolean {
        if (!isEllipseMeasuring) {
            Log.w(TAG, "⚠️ Ellipse measurement not active")
            return false
        }

        try {
            val measurementId = ellipseMeasureHelper.startNewMeasurement()
            updateEllipseMeasurementDisplay()
            forceOverlayDisplay()
            Log.d(TAG, "✅ New ellipse measurement added, ID: $measurementId")
            return true
        } catch (e: Exception) {
            Log.e(TAG, "❌ Failed to add new ellipse measurement: ${e.message}")
            return false
        }
    }

    /**
     * 🛑 停止三垂直测量
     */
    fun stopThreeVerticalMeasurement() {
        if (!isThreeVerticalMeasuring) {
            Log.d(TAG, "⚠️ Three vertical measurement not active")
            return
        }

        try {
            Log.d(TAG, "🛑 Stopping three vertical measurement")

            // 1. 清除选中状态
            threeVerticalMeasureHelper.clearSelection()

            // 2. 隐藏覆盖层
            overlayView.visibility = View.GONE

            // 3. 更新状态
            isThreeVerticalMeasuring = false

            // 4. 隐藏状态文本
            statusTextView?.visibility = View.GONE

            Log.d(TAG, "✅ Three vertical measurement stopped - data preserved")

        } catch (e: Exception) {
            Log.e(TAG, "❌ Failed to stop three vertical measurement: ${e.message}")
            e.printStackTrace()
        }
    }

    /**
     * 🛑 停止矩形测量
     */
    fun stopRectangleMeasurement() {
        if (!isRectangleMeasuring) {
            Log.d(TAG, "⚠️ Rectangle measurement not active")
            return
        }

        try {
            Log.d(TAG, "🛑 Stopping rectangle measurement")

            // 1. 清除选中状态
            rectangleMeasureHelper.clearSelection()

            // 2. 隐藏覆盖层
            overlayView.visibility = View.GONE

            // 3. 更新状态
            isRectangleMeasuring = false

            // 4. 隐藏状态文本
            statusTextView?.visibility = View.GONE

            Log.d(TAG, "✅ Rectangle measurement stopped - data preserved")

        } catch (e: Exception) {
            Log.e(TAG, "❌ Failed to stop rectangle measurement: ${e.message}")
            e.printStackTrace()
        }
    }

    /**
     * 🛑 停止椭圆测量
     */
    fun stopEllipseMeasurement() {
        if (!isEllipseMeasuring) {
            Log.d(TAG, "⚠️ Ellipse measurement not active")
            return
        }

        try {
            Log.d(TAG, "🛑 Stopping ellipse measurement")

            // 1. 清除选中状态
            ellipseMeasureHelper.clearSelection()

            // 2. 隐藏覆盖层
            overlayView.visibility = View.GONE

            // 3. 更新状态
            isEllipseMeasuring = false

            // 4. 隐藏状态文本
            statusTextView?.visibility = View.GONE

            Log.d(TAG, "✅ Ellipse measurement stopped - data preserved")

        } catch (e: Exception) {
            Log.e(TAG, "❌ Failed to stop ellipse measurement: ${e.message}")
            e.printStackTrace()
        }
    }

    /**
     * 🎯 开始三点矩形测量 - 支持旋转的矩形测量
     */
    fun startThreeRectangleMeasurement(): Boolean {
        if (!isInitialized) {
            Log.e(TAG, "❌ MeasurementManager not initialized")
            return false
        }

        if (isThreeRectangleMeasuring) {
            Log.w(TAG, "⚠️ Three rectangle measurement already in progress")
            return true
        }

        try {
            Log.d(TAG, "🎯 Starting three rectangle measurement")

            // 1. 设置混合触摸监听器
            setupMixedTouchHandler()

            // 2. 初始化助手
            currentBitmap?.let { bitmap ->
                threeRectangleMeasureHelper.init(imageView, bitmap)
                threeRectangleMeasureHelper.setMeasurementUpdateCallback {
                    updateThreeRectangleMeasurementDisplay()
                }
            }

            // 3. 创建初始测量
            val measurementId = threeRectangleMeasureHelper.startNewMeasurement()
            if (measurementId.isNotEmpty()) {
                // 4. 更新状态
                isThreeRectangleMeasuring = true
                activeMeasurementMode = MeasurementMode.THREE_RECTANGLE

                // 5. 显示覆盖层
                forceOverlayDisplay()

                // 6. 更新显示
                updateThreeRectangleMeasurementDisplay()

                // 7. 显示状态文本
                statusTextView?.apply {
                    text = "三点矩形测量模式：拖拽控制点调整尺寸和旋转"
                    visibility = View.VISIBLE
                }

                Log.d(TAG, "✅ Three rectangle measurement started successfully: $measurementId")
                return true
            } else {
                Log.e(TAG, "❌ Failed to create initial three rectangle measurement")
                return false
            }

        } catch (e: Exception) {
            Log.e(TAG, "❌ Failed to start three rectangle measurement: ${e.message}")
            e.printStackTrace()
            return false
        }
    }

    /**
     * 🎯 添加新的三点矩形测量
     */
    fun addNewThreeRectangleMeasurement(): String {
        if (!isThreeRectangleMeasuring) {
            Log.w(TAG, "⚠️ Three rectangle measurement not active")
            return ""
        }

        try {
            val measurementId = threeRectangleMeasureHelper.addNewMeasurement()
            updateThreeRectangleMeasurementDisplay()
            Log.d(TAG, "✅ New three rectangle measurement added: $measurementId")
            return measurementId

        } catch (e: Exception) {
            Log.e(TAG, "❌ Failed to add new three rectangle measurement: ${e.message}")
            return ""
        }
    }

    /**
     * 🛑 停止三点矩形测量
     */
    fun stopThreeRectangleMeasurement() {
        if (!isThreeRectangleMeasuring) {
            Log.d(TAG, "⚠️ Three rectangle measurement not active")
            return
        }

        try {
            Log.d(TAG, "🛑 Stopping three rectangle measurement")

            // 1. 清除选中状态
            threeRectangleMeasureHelper.clearSelection()

            // 2. 隐藏覆盖层
            overlayView.visibility = View.GONE

            // 3. 更新状态
            isThreeRectangleMeasuring = false

            // 4. 隐藏状态文本
            statusTextView?.visibility = View.GONE

            Log.d(TAG, "✅ Three rectangle measurement stopped - data preserved")

        } catch (e: Exception) {
            Log.e(TAG, "❌ Failed to stop three rectangle measurement: ${e.message}")
            e.printStackTrace()
        }
    }

    /**
     * 🎯 强制覆盖层显示 - 复制yolo_demo的成功机制
     */
    private fun forceOverlayDisplay() {
        overlayView.visibility = View.VISIBLE
        overlayView.bringToFront()
        overlayView.invalidate()
        overlayView.requestLayout()
        Log.d(TAG, "🎯 Forced overlay display with multiple mechanisms")
    }

    /**
     * 🔄 更新覆盖层显示 - 支持多角度同时显示
     */
    private fun updateOverlayDisplay() {
        try {
            Log.d(TAG, "🔄 [DEBUG] updateOverlayDisplay called - isAngleMeasuring: $isAngleMeasuring")

            if (!isAngleMeasuring) {
                Log.d(TAG, "⚠️ [DEBUG] Not in angle measuring mode, skipping overlay update")
                return
            }

            Log.d(TAG, "📊 [DEBUG] Getting measurement data from helper...")
            val measurementData = angleMeasureHelper.getAllMeasurementData()
            Log.d(TAG, "📊 [DEBUG] Retrieved ${measurementData.size} measurements from helper")

            if (measurementData.isNotEmpty()) {
                // 🎯 显示所有角度测量
                Log.d(TAG, "🎯 [DEBUG] Setting all angle measurement data to overlay...")
                overlayView.setAllAngleMeasurementData(measurementData)

                measurementData.forEachIndexed { index, measurement ->
                    Log.d(TAG, "📊 [DEBUG] measurement[$index]: points.size=${measurement.points.size}, angle=${measurement.angle}°, isDragging=${measurement.isDragging}")
                }

                // 🎯 复制yolo_demo的多重强制显示机制
                Log.d(TAG, "🎯 [DEBUG] Forcing overlay display...")
                forceOverlayDisplay()

                // 🔄 延迟再次强制显示
                overlayView.post {
                    Log.d(TAG, "🎯 [DEBUG] Post-delayed force overlay display...")
                    forceOverlayDisplay()
                }

                Log.d(TAG, "✅ [DEBUG] Multi-angle overlay display updated successfully")
            } else {
                Log.w(TAG, "⚠️ [DEBUG] No measurement data available for overlay update - measurementData is empty!")
                Log.d(TAG, "⚠️ [DEBUG] Helper state - measurements count: ${angleMeasureHelper.getMeasurementCount()}")
                overlayView.clearMeasurement()
            }

        } catch (e: Exception) {
            Log.e(TAG, "❌ [DEBUG] Failed to update overlay: ${e.message}")
            e.printStackTrace()
        }
    }

    /**
     * 🔄 更新点测量覆盖层显示
     */
    private fun updatePointMeasurementDisplay() {
        try {
            Log.d(TAG, "🔄 [DEBUG] updatePointMeasurementDisplay called - isPointMeasuring: $isPointMeasuring")

            if (!isPointMeasuring) {
                Log.d(TAG, "⚠️ [DEBUG] Not in point measuring mode, skipping overlay update")
                return
            }

            Log.d(TAG, "📊 [DEBUG] Getting point measurement data from helper...")
            val measurementData = pointMeasureHelper.getAllMeasurementData()
            Log.d(TAG, "📊 [DEBUG] Retrieved ${measurementData.size} point measurements from helper")

            if (measurementData.isNotEmpty()) {
                Log.d(TAG, "📊 [DEBUG] Updating overlay with ${measurementData.size} point measurements")

                // 🎯 更新覆盖层的点测量数据
                overlayView.updatePointMeasurements(measurementData)

                // 🎯 复制yolo_demo的多重强制显示机制
                Log.d(TAG, "🎯 [DEBUG] Forcing overlay display...")
                forceOverlayDisplay()

                // 🔄 延迟再次强制显示
                overlayView.post {
                    Log.d(TAG, "🎯 [DEBUG] Post-delayed force overlay display...")
                    forceOverlayDisplay()
                }

                Log.d(TAG, "✅ [DEBUG] Point measurement overlay display updated successfully")
            } else {
                Log.w(TAG, "⚠️ [DEBUG] No point measurement data available for overlay update - measurementData is empty!")
                Log.d(TAG, "⚠️ [DEBUG] Helper state - measurements count: ${pointMeasureHelper.getMeasurementCount()}")
                overlayView.clearMeasurement()
            }

        } catch (e: Exception) {
            Log.e(TAG, "❌ [DEBUG] Failed to update point measurement overlay: ${e.message}")
            e.printStackTrace()
        }
    }

    /**
     * 📏 更新线段测量覆盖层显示
     */
    private fun updateLineMeasurementDisplay() {
        try {
            Log.d(TAG, "🔄 [DEBUG] updateLineMeasurementDisplay called - isLineMeasuring: $isLineMeasuring")

            if (!isLineMeasuring) {
                Log.d(TAG, "⚠️ [DEBUG] Not in line measuring mode, skipping overlay update")
                return
            }

            Log.d(TAG, "📊 [DEBUG] Getting line measurement data from helper...")
            val measurementData = lineMeasureHelper.getAllMeasurementData()
            Log.d(TAG, "📊 [DEBUG] Retrieved ${measurementData.size} line measurements from helper")

            if (measurementData.isNotEmpty()) {
                Log.d(TAG, "📊 [DEBUG] Updating overlay with ${measurementData.size} line measurements")

                // 📏 更新覆盖层的线段测量数据
                overlayView.updateLineMeasurements(measurementData)

                // 🎯 复制yolo_demo的多重强制显示机制
                Log.d(TAG, "🎯 [DEBUG] Forcing overlay display...")
                forceOverlayDisplay()

                // 🔄 延迟再次强制显示
                overlayView.post {
                    Log.d(TAG, "🎯 [DEBUG] Post-delayed force overlay display...")
                    forceOverlayDisplay()
                }

                Log.d(TAG, "✅ [DEBUG] Line measurement overlay display updated successfully")
            } else {
                Log.w(TAG, "⚠️ [DEBUG] No line measurement data available for overlay update - measurementData is empty!")
                Log.d(TAG, "⚠️ [DEBUG] Helper state - measurements count: ${lineMeasureHelper.getMeasurementCount()}")
                overlayView.clearMeasurement()
            }

        } catch (e: Exception) {
            Log.e(TAG, "❌ [DEBUG] Failed to update line measurement display: ${e.message}")
            e.printStackTrace()
        }
    }

    /**
     * 📏 更新水平线测量覆盖层显示
     */
    private fun updateHorizonLineMeasurementDisplay() {
        try {
            Log.d(TAG, "🔄 [DEBUG] updateHorizonLineMeasurementDisplay called - isHorizonLineMeasuring: $isHorizonLineMeasuring")

            if (!isHorizonLineMeasuring) {
                Log.d(TAG, "⚠️ [DEBUG] Not in horizon line measuring mode, skipping overlay update")
                return
            }

            Log.d(TAG, "📊 [DEBUG] Getting horizon line measurement data from helper...")
            val measurementData = horizonLineMeasureHelper.getAllMeasurementData()
            Log.d(TAG, "📊 [DEBUG] Retrieved ${measurementData.size} horizon line measurements from helper")

            if (measurementData.isNotEmpty()) {
                Log.d(TAG, "📊 [DEBUG] Updating overlay with ${measurementData.size} horizon line measurements")

                // 📏 更新覆盖层的水平线测量数据
                overlayView.updateHorizonLineMeasurements(measurementData)

                // 🎯 复制yolo_demo的多重强制显示机制
                Log.d(TAG, "🎯 [DEBUG] Forcing overlay display...")
                forceOverlayDisplay()

                // 延迟再次强制显示确保时序
                overlayView.post {
                    Log.d(TAG, "🎯 [DEBUG] Post-delayed force overlay display...")
                    forceOverlayDisplay()
                }

                Log.d(TAG, "✅ [DEBUG] Horizon line measurement overlay display updated successfully")
            } else {
                Log.w(TAG, "⚠️ [DEBUG] No horizon line measurement data available for overlay update - measurementData is empty!")
                Log.d(TAG, "⚠️ [DEBUG] Helper state - measurements count: ${horizonLineMeasureHelper.getMeasurementCount()}")
                overlayView.clearMeasurement()
            }

        } catch (e: Exception) {
            Log.e(TAG, "❌ [DEBUG] Failed to update horizon line measurement display: ${e.message}")
            e.printStackTrace()
        }
    }

    /**
     * 📏 更新垂直线测量覆盖层显示
     */
    private fun updateVerticalLineMeasurementDisplay() {
        try {
            Log.d(TAG, "🔄 [DEBUG] updateVerticalLineMeasurementDisplay called - isVerticalLineMeasuring: $isVerticalLineMeasuring")

            if (!isVerticalLineMeasuring) {
                Log.d(TAG, "⚠️ [DEBUG] Not in vertical line measuring mode, skipping overlay update")
                return
            }

            Log.d(TAG, "📊 [DEBUG] Getting vertical line measurement data from helper...")
            val measurementData = verticalLineMeasureHelper.getAllMeasurementData()
            Log.d(TAG, "📊 [DEBUG] Retrieved ${measurementData.size} vertical line measurements from helper")

            if (measurementData.isNotEmpty()) {
                Log.d(TAG, "📊 [DEBUG] Updating overlay with ${measurementData.size} vertical line measurements")

                // 📏 更新覆盖层的垂直线测量数据
                overlayView.updateVerticalLineMeasurements(measurementData)

                // 🎯 复制yolo_demo的多重强制显示机制
                Log.d(TAG, "🎯 [DEBUG] Forcing overlay display...")
                forceOverlayDisplay()

                // 延迟再次强制显示确保时序
                overlayView.post {
                    Log.d(TAG, "🎯 [DEBUG] Post-delayed force overlay display...")
                    forceOverlayDisplay()
                }

                Log.d(TAG, "✅ [DEBUG] Vertical line measurement overlay display updated successfully")
            } else {
                Log.w(TAG, "⚠️ [DEBUG] No vertical line measurement data available for overlay update - measurementData is empty!")
                Log.d(TAG, "⚠️ [DEBUG] Helper state - measurements count: ${verticalLineMeasureHelper.getMeasurementCount()}")
                overlayView.clearMeasurement()
            }

        } catch (e: Exception) {
            Log.e(TAG, "❌ [DEBUG] Failed to update vertical line measurement display: ${e.message}")
            e.printStackTrace()
        }
    }

    /**
     * 📏 更新平行线测量覆盖层显示
     */
    private fun updateParallelLinesMeasurementDisplay() {
        try {
            Log.d(TAG, "🔄 [DEBUG] updateParallelLinesMeasurementDisplay called - isParallelLinesMeasuring: $isParallelLinesMeasuring")

            if (!isParallelLinesMeasuring) {
                Log.d(TAG, "⚠️ [DEBUG] Not in parallel lines measuring mode, skipping overlay update")
                return
            }

            Log.d(TAG, "📊 [DEBUG] Getting parallel lines measurement data from helper...")
            val measurementData = parallelLinesMeasureHelper.getAllMeasurementData()
            Log.d(TAG, "📊 [DEBUG] Retrieved ${measurementData.size} parallel lines measurements from helper")

            if (measurementData.isNotEmpty()) {
                Log.d(TAG, "📊 [DEBUG] Updating overlay with ${measurementData.size} parallel lines measurements")

                // 📏 更新覆盖层的平行线测量数据
                overlayView.updateParallelLinesMeasurements(measurementData)

                // 🎯 复制yolo_demo的多重强制显示机制
                Log.d(TAG, "🎯 [DEBUG] Forcing overlay display...")
                forceOverlayDisplay()

                // 延迟再次强制显示确保时序
                overlayView.post {
                    Log.d(TAG, "🎯 [DEBUG] Post-delayed force overlay display...")
                    forceOverlayDisplay()
                }

                Log.d(TAG, "✅ [DEBUG] Parallel lines measurement overlay display updated successfully")
            } else {
                Log.w(TAG, "⚠️ [DEBUG] No parallel lines measurement data available for overlay update - measurementData is empty!")
                Log.d(TAG, "⚠️ [DEBUG] Helper state - measurements count: ${parallelLinesMeasureHelper.getMeasurementCount()}")
                overlayView.clearMeasurement()
            }

        } catch (e: Exception) {
            Log.e(TAG, "❌ [DEBUG] Failed to update parallel lines measurement display: ${e.message}")
            e.printStackTrace()
        }
    }

    /**
     * 📏 更新三垂直测量覆盖层显示
     */
    private fun updateThreeVerticalMeasurementDisplay() {
        try {
            Log.d(TAG, "🔄 [DEBUG] updateThreeVerticalMeasurementDisplay called - isThreeVerticalMeasuring: $isThreeVerticalMeasuring")

            if (!isThreeVerticalMeasuring) {
                Log.d(TAG, "⚠️ [DEBUG] Not in three vertical measuring mode, skipping overlay update")
                return
            }

            Log.d(TAG, "📊 [DEBUG] Getting three vertical measurement data from helper...")
            val measurementData = threeVerticalMeasureHelper.getAllMeasurementData()
            Log.d(TAG, "📊 [DEBUG] Retrieved ${measurementData.size} three vertical measurements from helper")

            if (measurementData.isNotEmpty()) {
                // 🎯 显示所有三垂直测量
                Log.d(TAG, "🎯 [DEBUG] Setting all three vertical measurement data to overlay...")
                overlayView.setAllThreeVerticalMeasurementData(measurementData)

                measurementData.forEachIndexed { index, measurement ->
                    Log.d(TAG, "📊 [DEBUG] three vertical measurement[$index]: points.size=${measurement.viewPoints.size}, distance=${measurement.distance}px, isSelected=${measurement.isSelected}")
                }

                // 🎯 复制其他测量的多重强制显示机制
                Log.d(TAG, "🎯 [DEBUG] Forcing overlay display...")
                forceOverlayDisplay()

                // 延迟再次强制显示确保时序
                overlayView.post {
                    Log.d(TAG, "🎯 [DEBUG] Post-delayed force overlay display...")
                    forceOverlayDisplay()
                }

                Log.d(TAG, "✅ [DEBUG] Three vertical measurement overlay display updated successfully")
            } else {
                Log.w(TAG, "⚠️ [DEBUG] No three vertical measurement data available for overlay update - measurementData is empty!")
                Log.d(TAG, "⚠️ [DEBUG] Helper state - measurements count: ${threeVerticalMeasureHelper.getMeasurementCount()}")
                overlayView.clearMeasurement()
            }

        } catch (e: Exception) {
            Log.e(TAG, "❌ [DEBUG] Failed to update three vertical measurement display: ${e.message}")
            e.printStackTrace()
        }
    }

    /**
     * 📦 更新矩形测量覆盖层显示
     */
    private fun updateRectangleMeasurementDisplay() {
        try {
            Log.d(TAG, "🔄 [DEBUG] updateRectangleMeasurementDisplay called - isRectangleMeasuring: $isRectangleMeasuring")

            if (!isRectangleMeasuring) {
                Log.d(TAG, "⚠️ [DEBUG] Not in rectangle measuring mode, skipping overlay update")
                return
            }

            Log.d(TAG, "📊 [DEBUG] Getting rectangle measurement data from helper...")
            val measurementData = rectangleMeasureHelper.getAllMeasurements()
            Log.d(TAG, "📊 [DEBUG] Retrieved ${measurementData.size} rectangle measurements from helper")

            if (measurementData.isNotEmpty()) {
                // 🎯 显示所有矩形测量
                Log.d(TAG, "🎯 [DEBUG] Setting all rectangle measurement data to overlay...")
                overlayView.updateRectangleMeasurements(rectangleMeasureHelper.getAllMeasurements())

                measurementData.forEachIndexed { index, measurement ->
                    Log.d(TAG, "📊 [DEBUG] rectangle measurement[$index]: points.size=${measurement.viewPoints.size}, area=${measurement.area}px², perimeter=${measurement.perimeter}px, isSelected=${measurement.isSelected}")
                }

                // 🎯 复制其他测量的多重强制显示机制
                Log.d(TAG, "🎯 [DEBUG] Forcing overlay display...")
                forceOverlayDisplay()

                // 延迟再次强制显示确保时序
                overlayView.post {
                    Log.d(TAG, "🎯 [DEBUG] Post-delayed force overlay display...")
                    forceOverlayDisplay()
                }

                Log.d(TAG, "✅ [DEBUG] Rectangle measurement overlay display updated successfully")
            } else {
                Log.w(TAG, "⚠️ [DEBUG] No rectangle measurement data available for overlay update - measurementData is empty!")
                Log.d(TAG, "⚠️ [DEBUG] Helper state - measurements count: ${rectangleMeasureHelper.getMeasurementCount()}")
                overlayView.clearMeasurement()
            }

        } catch (e: Exception) {
            Log.e(TAG, "❌ [DEBUG] Failed to update rectangle measurement display: ${e.message}")
            e.printStackTrace()
        }
    }

    /**
     * 🔵 更新椭圆测量覆盖层显示
     */
    private fun updateEllipseMeasurementDisplay() {
        try {
            Log.d(TAG, "🔄 [DEBUG] updateEllipseMeasurementDisplay called - isEllipseMeasuring: $isEllipseMeasuring")

            if (!isEllipseMeasuring) {
                Log.d(TAG, "⚠️ [DEBUG] Not in ellipse measuring mode, skipping overlay update")
                return
            }

            Log.d(TAG, "📊 [DEBUG] Getting ellipse measurement data from helper...")
            val measurementData = ellipseMeasureHelper.getAllMeasurements()
            Log.d(TAG, "📊 [DEBUG] Retrieved ${measurementData.size} ellipse measurements from helper")

            if (measurementData.isNotEmpty()) {
                // 🎯 显示所有椭圆测量
                Log.d(TAG, "🎯 [DEBUG] Setting all ellipse measurement data to overlay...")
                overlayView.updateEllipseMeasurements(ellipseMeasureHelper.getAllMeasurements())

                measurementData.forEachIndexed { index, measurement ->
                    Log.d(TAG, "📊 [DEBUG] ellipse measurement[$index]: points.size=${measurement.viewPoints.size}, area=${measurement.calculateArea()}px², perimeter=${measurement.calculatePerimeter()}px, isSelected=${measurement.isSelected}")
                }

                // 🎯 复制其他测量的多重强制显示机制
                Log.d(TAG, "🎯 [DEBUG] Forcing overlay display...")
                forceOverlayDisplay()

                // 延迟再次强制显示确保时序
                overlayView.post {
                    Log.d(TAG, "🎯 [DEBUG] Post-delayed force overlay display...")
                    forceOverlayDisplay()
                }

                Log.d(TAG, "✅ [DEBUG] Ellipse measurement overlay display updated successfully")
            } else {
                Log.w(TAG, "⚠️ [DEBUG] No ellipse measurement data available for overlay update - measurementData is empty!")
                Log.d(TAG, "⚠️ [DEBUG] Helper state - measurements count: ${ellipseMeasureHelper.getMeasurementCount()}")
                overlayView.clearMeasurement()
            }

        } catch (e: Exception) {
            Log.e(TAG, "❌ [DEBUG] Failed to update ellipse measurement display: ${e.message}")
            e.printStackTrace()
        }
    }

    /**
     * 🎯 更新三点矩形测量覆盖层显示
     */
    private fun updateThreeRectangleMeasurementDisplay() {
        try {
            Log.d(TAG, "🔄 [DEBUG] updateThreeRectangleMeasurementDisplay called - isThreeRectangleMeasuring: $isThreeRectangleMeasuring")

            if (!isThreeRectangleMeasuring) {
                Log.d(TAG, "⚠️ [DEBUG] Not in three rectangle measuring mode, skipping overlay update")
                return
            }

            Log.d(TAG, "📊 [DEBUG] Getting three rectangle measurement data from helper...")
            val measurementData = threeRectangleMeasureHelper.getAllMeasurements()
            Log.d(TAG, "📊 [DEBUG] Retrieved ${measurementData.size} three rectangle measurements from helper")

            if (measurementData.isNotEmpty()) {
                // 🎯 显示所有三点矩形测量
                Log.d(TAG, "🎯 [DEBUG] Setting all three rectangle measurement data to overlay...")
                overlayView.updateThreeRectangleMeasurements(measurementData)

                measurementData.forEachIndexed { index, measurement ->
                    Log.d(TAG, "📊 [DEBUG] three rectangle measurement[$index]: center=(${measurement.centerPoint.x}, ${measurement.centerPoint.y}), size=${measurement.width}x${measurement.height}, rotation=${measurement.rotationAngle}, area=${measurement.area}px², perimeter=${measurement.perimeter}px, isSelected=${measurement.isSelected}")
                }

                // 🎯 复制其他测量的多重强制显示机制
                Log.d(TAG, "🎯 [DEBUG] Forcing overlay display...")
                forceOverlayDisplay()

                // 延迟再次强制显示确保时序
                overlayView.post {
                    Log.d(TAG, "🎯 [DEBUG] Post-delayed force overlay display...")
                    forceOverlayDisplay()
                }

                Log.d(TAG, "✅ [DEBUG] Three rectangle measurement overlay display updated successfully")
            } else {
                Log.w(TAG, "⚠️ [DEBUG] No three rectangle measurement data available for overlay update - measurementData is empty!")
                Log.d(TAG, "⚠️ [DEBUG] Helper state - measurements count: ${threeRectangleMeasureHelper.getMeasurementCount()}")
                overlayView.clearMeasurement()
            }

        } catch (e: Exception) {
            Log.e(TAG, "❌ [DEBUG] Failed to update three rectangle measurement display: ${e.message}")
            e.printStackTrace()
        }
    }

    /**
     * 🔄 更新四点角度覆盖层显示
     */
    private fun updateFourPointOverlayDisplay() {
        try {
            Log.d(TAG, "🔄 [DEBUG] updateFourPointOverlayDisplay called - isFourPointAngleMeasuring: $isFourPointAngleMeasuring")

            if (!isFourPointAngleMeasuring) {
                Log.d(TAG, "⚠️ [DEBUG] Not in four-point angle measuring mode, skipping overlay update")
                return
            }

            Log.d(TAG, "📊 [DEBUG] Getting four-point measurement data from helper...")
            val measurementData = fourPointAngleHelper.getAllMeasurementData()
            Log.d(TAG, "📊 [DEBUG] Retrieved ${measurementData.size} four-point measurements from helper")

            if (measurementData.isNotEmpty()) {
                // 🎯 显示所有四点角度测量
                Log.d(TAG, "🎯 [DEBUG] Setting all four-point angle measurement data to overlay...")
                overlayView.setAllFourPointAngleMeasurementData(measurementData)

                measurementData.forEachIndexed { index, measurement ->
                    Log.d(TAG, "📊 [DEBUG] four-point measurement[$index]: points.size=${measurement.points.size}, angle=${measurement.angle}°, isDragging=${measurement.isDragging}")
                }

                // 🎯 强制显示机制
                Log.d(TAG, "🎯 [DEBUG] Forcing overlay display...")
                forceOverlayDisplay()

                // 🔄 延迟再次强制显示
                overlayView.post {
                    Log.d(TAG, "🎯 [DEBUG] Post-delayed force overlay display...")
                    forceOverlayDisplay()
                }

            } else {
                Log.d(TAG, "⚠️ [DEBUG] No four-point measurement data available")
                overlayView.clearMeasurement()
            }

        } catch (e: Exception) {
            Log.e(TAG, "❌ Failed to update four-point overlay display: ${e.message}")
            e.printStackTrace()
        }
    }

    /**
     * 🔄 更新混合覆盖层显示 - 基于数据存在性显示，支持数据持久化
     */
    private fun updateMixedOverlayDisplay() {
        try {
            // Log.d(TAG, "🔄 [DEBUG] updateMixedOverlayDisplay called - isAngleMeasuring: $isAngleMeasuring, isFourPointAngleMeasuring: $isFourPointAngleMeasuring")

            if (!isMixedMeasuring()) {
                // Log.d(TAG, "⚠️ [DEBUG] Not in any measurement mode, skipping overlay update")
                return
            }

            // 确保覆盖层可见
            overlayView.visibility = View.VISIBLE

            // 🎯 获取三点角度测量数据
            val angleMeasurements = angleMeasureHelper.getAllMeasurementData()
            val isAngleActive = (activeMeasurementMode == MeasurementMode.ANGLE)
            overlayView.updateAngleMeasurements(angleMeasurements)
            // Log.d(TAG, "🔄 [DEBUG] Updated ${angleMeasurements.size} three-point angle measurements (active: $isAngleActive)")

            // 🎯 获取四点角度测量数据
            val fourPointMeasurements = fourPointAngleHelper.getAllMeasurementData()
            val isFourPointActive = (activeMeasurementMode == MeasurementMode.FOUR_POINT_ANGLE)
            overlayView.updateFourPointAngleMeasurements(fourPointMeasurements)
            // Log.d(TAG, "🔄 [DEBUG] Updated ${fourPointMeasurements.size} four-point angle measurements (active: $isFourPointActive)")

            // 🎯 获取点测量数据
            val pointMeasurements = pointMeasureHelper.getAllMeasurementData()
            val isPointActive = (activeMeasurementMode == MeasurementMode.POINT)
            overlayView.updatePointMeasurements(pointMeasurements)
            // Log.d(TAG, "🔄 [DEBUG] Updated ${pointMeasurements.size} point measurements (active: $isPointActive)")

            // 🎯 获取线段测量数据
            val lineMeasurements = lineMeasureHelper.getAllMeasurementData()
            val isLineActive = (activeMeasurementMode == MeasurementMode.LINE)
            overlayView.updateLineMeasurements(lineMeasurements)
            // Log.d(TAG, "🔄 [DEBUG] Updated ${lineMeasurements.size} line measurements (active: $isLineActive)")

            // 🎯 获取水平线测量数据
            val horizonLineMeasurements = horizonLineMeasureHelper.getAllMeasurementData()
            val isHorizonLineActive = (activeMeasurementMode == MeasurementMode.HORIZON_LINE)
            overlayView.updateHorizonLineMeasurements(horizonLineMeasurements)
            // Log.d(TAG, "🔄 [DEBUG] Updated ${horizonLineMeasurements.size} horizon line measurements (active: $isHorizonLineActive)")

            // 🎯 获取垂直线测量数据
            val verticalLineMeasurements = verticalLineMeasureHelper.getAllMeasurementData()
            val isVerticalLineActive = (activeMeasurementMode == MeasurementMode.VERTICAL_LINE)
            overlayView.updateVerticalLineMeasurements(verticalLineMeasurements)
            // Log.d(TAG, "🔄 [DEBUG] Updated ${verticalLineMeasurements.size} vertical line measurements (active: $isVerticalLineActive)")

            // 🎯 获取平行线测量数据
            val parallelLinesMeasurements = parallelLinesMeasureHelper.getAllMeasurementData()
            val isParallelLinesActive = (activeMeasurementMode == MeasurementMode.PARALLEL_LINES)
            overlayView.updateParallelLinesMeasurements(parallelLinesMeasurements)
            // Log.d(TAG, "🔄 [DEBUG] Updated ${parallelLinesMeasurements.size} parallel lines measurements (active: $isParallelLinesActive)")

            val threeVerticalMeasurements = threeVerticalMeasureHelper.getAllMeasurementData()
            val isThreeVerticalActive = (activeMeasurementMode == MeasurementMode.THREE_VERTICAL)
            overlayView.updateThreeVerticalMeasurements(threeVerticalMeasurements)

            // 🎯 获取矩形测量数据
            val rectangleMeasurements = rectangleMeasureHelper.getAllMeasurements()
            val isRectangleActive = (activeMeasurementMode == MeasurementMode.RECTANGLE)
            overlayView.updateRectangleMeasurements(rectangleMeasurements)
            // Log.d(TAG, "🔄 [DEBUG] Updated ${rectangleMeasurements.size} rectangle measurements (active: $isRectangleActive)")

            // 🎯 获取三点矩形测量数据
            val threeRectangleMeasurements = threeRectangleMeasureHelper.getAllMeasurements()
            val isThreeRectangleActive = (activeMeasurementMode == MeasurementMode.THREE_RECTANGLE)
            overlayView.updateThreeRectangleMeasurements(threeRectangleMeasurements)
            // Log.d(TAG, "🔄 [DEBUG] Updated ${threeRectangleMeasurements.size} three rectangle measurements (active: $isThreeRectangleActive)")

            // 🎯 获取椭圆测量数据
            val ellipseMeasurements = ellipseMeasureHelper.getAllMeasurements()
            val isEllipseActive = (activeMeasurementMode == MeasurementMode.ELLIPSE)
            overlayView.updateEllipseMeasurements(ellipseMeasurements)
            // Log.d(TAG, "🔄 [DEBUG] Updated ${ellipseMeasurements.size} ellipse measurements (active: $isEllipseActive)")

            // 强制重绘覆盖层
            overlayView.invalidate()

            // Log.d(TAG, "✅ [DEBUG] Mixed overlay display updated successfully - persistent data display enabled")

        } catch (e: Exception) {
            Log.e(TAG, "❌ Failed to update mixed overlay display: ${e.message}")
            e.printStackTrace()
        }
    }

    /**
     * 🚀 设置四点角度混合触摸处理器
     */
    private fun setupFourPointHybridTouchHandler() {
        // 恢复TpImageView的原始触摸监听器（保持缩放功能）
        imageView.restoreTouchListener()

        // 设置四点角度测量触摸处理器
        val measurementHandler = { event: android.view.MotionEvent, viewWidth: Int, viewHeight: Int ->
            var handled = false

            if (isFourPointAngleMeasuring) {
                handled = fourPointAngleHelper.handleTouchEvent(event, viewWidth, viewHeight)
            }

            handled
        }

        // 将测量处理器设置到TpImageView
        imageView.setMeasurementTouchHandler(measurementHandler)

        Log.d(TAG, "🚀 Four-point hybrid touch handler setup complete")
    }

    /**
     * 🚀 设置混合触摸处理器（支持缩放+测量）
     */
    private fun setupHybridTouchHandler() {
        // 恢复TpImageView的原始触摸监听器（保持缩放功能）
        imageView.restoreTouchListener()
        
        // 设置测量触摸处理器
        val measurementHandler = { event: android.view.MotionEvent, viewWidth: Int, viewHeight: Int ->
            var handled = false

            if (isAngleMeasuring) {
                handled = angleMeasureHelper.handleTouchEvent(event, viewWidth, viewHeight)
            }

            handled
        }
        
        // 将测量处理器设置到TpImageView
        imageView.setMeasurementTouchHandler(measurementHandler)
        
        Log.d(TAG, "🚀 Hybrid touch handler setup complete")
    }

    /**
     * 🚀 设置混合模式触摸处理器 - 智能激活对应的测量类型
     */
    private fun setupMixedTouchHandler() {
        // 恢复TpImageView的原始触摸监听器（保持缩放功能）
        imageView.restoreTouchListener()

        // 创建智能混合测量触摸处理器
        val mixedMeasurementHandler = { event: android.view.MotionEvent, viewWidth: Int, viewHeight: Int ->
            Log.d(TAG, "🎯 MeasurementManager smart touch handler called: action=${event.action}, point=(${event.x}, ${event.y})")
            var handled = false

            if (event.action == android.view.MotionEvent.ACTION_DOWN) {
                // 🎯 智能检测：检查触摸点是否在某个测量上，自动激活对应模式
                val touchPoint = android.graphics.PointF(event.x, event.y)

                // 首先检查三点角度测量
                if (isAngleMeasuring && angleMeasureHelper.isPointOnMeasurement(touchPoint)) {
                    if (activeMeasurementMode != MeasurementMode.ANGLE) {
                        Log.d(TAG, "🎯 Touch detected on three-point angle measurement - auto-activating ANGLE mode")
                        // 🔄 清除其他模式的选中状态
                        clearOtherModeSelections(MeasurementMode.ANGLE)
                        activeMeasurementMode = MeasurementMode.ANGLE
                        updateMixedOverlayDisplay()
                    }
                    Log.d(TAG, "🎯 Delegating touch to AngleMeasureHelper for three-point angle measurement")
                    handled = angleMeasureHelper.handleTouchEvent(event, viewWidth, viewHeight)
                    Log.d(TAG, "🎯 AngleMeasureHelper.handleTouchEvent returned: $handled")
                }
                // 然后检查四点角度测量
                else if (isFourPointAngleMeasuring && fourPointAngleHelper.isPointOnMeasurement(touchPoint)) {
                    if (activeMeasurementMode != MeasurementMode.FOUR_POINT_ANGLE) {
                        Log.d(TAG, "🎯 Touch detected on four-point angle measurement - auto-activating FOUR_POINT_ANGLE mode")
                        // 🔄 清除其他模式的选中状态
                        clearOtherModeSelections(MeasurementMode.FOUR_POINT_ANGLE)
                        activeMeasurementMode = MeasurementMode.FOUR_POINT_ANGLE
                        updateMixedOverlayDisplay()
                    }
                    Log.d(TAG, "🎯 Delegating touch to FourPointAngleHelper for four-point angle measurement")
                    handled = fourPointAngleHelper.handleTouchEvent(event, viewWidth, viewHeight)
                    Log.d(TAG, "🎯 FourPointAngleHelper.handleTouchEvent returned: $handled")
                }
                // 然后检查点测量
                else if (isPointMeasuring && pointMeasureHelper.isPointOnMeasurement(touchPoint)) {
                    if (activeMeasurementMode != MeasurementMode.POINT) {
                        Log.d(TAG, "🎯 Touch detected on point measurement - auto-activating POINT mode")
                        // 🔄 清除其他模式的选中状态
                        clearOtherModeSelections(MeasurementMode.POINT)
                        activeMeasurementMode = MeasurementMode.POINT
                        updateMixedOverlayDisplay()
                    }
                    Log.d(TAG, "🎯 Delegating touch to PointMeasureHelper for point measurement")
                    handled = pointMeasureHelper.handleTouchEvent(event, viewWidth, viewHeight)
                    Log.d(TAG, "🎯 PointMeasureHelper.handleTouchEvent returned: $handled")
                }
                // 然后检查线段测量
                else if (isLineMeasuring && lineMeasureHelper.isPointOnMeasurement(touchPoint)) {
                    if (activeMeasurementMode != MeasurementMode.LINE) {
                        Log.d(TAG, "🎯 Touch detected on line measurement - auto-activating LINE mode")
                        // 🔄 清除其他模式的选中状态
                        clearOtherModeSelections(MeasurementMode.LINE)
                        activeMeasurementMode = MeasurementMode.LINE
                        updateMixedOverlayDisplay()
                    }
                    Log.d(TAG, "🎯 Delegating touch to LineMeasureHelper for line measurement")
                    handled = lineMeasureHelper.handleTouchEvent(event, viewWidth, viewHeight)
                    Log.d(TAG, "🎯 LineMeasureHelper.handleTouchEvent returned: $handled")
                }
                // 最后检查水平线测量
                else if (isHorizonLineMeasuring && horizonLineMeasureHelper.isPointOnMeasurement(touchPoint)) {
                    if (activeMeasurementMode != MeasurementMode.HORIZON_LINE) {
                        Log.d(TAG, "🎯 Touch detected on horizon line measurement - auto-activating HORIZON_LINE mode")
                        // 🔄 清除其他模式的选中状态
                        clearOtherModeSelections(MeasurementMode.HORIZON_LINE)
                        activeMeasurementMode = MeasurementMode.HORIZON_LINE
                        updateMixedOverlayDisplay()
                    }
                    Log.d(TAG, "🎯 Delegating touch to HorizonLineMeasureHelper for horizon line measurement")
                    handled = horizonLineMeasureHelper.handleTouchEvent(event, viewWidth, viewHeight)
                    Log.d(TAG, "🎯 HorizonLineMeasureHelper.handleTouchEvent returned: $handled")
                }
                // 然后检查垂直线测量
                else if (isVerticalLineMeasuring && verticalLineMeasureHelper.isPointOnMeasurement(touchPoint)) {
                    if (activeMeasurementMode != MeasurementMode.VERTICAL_LINE) {
                        Log.d(TAG, "🎯 Touch detected on vertical line measurement - auto-activating VERTICAL_LINE mode")
                        // 🔄 清除其他模式的选中状态
                        clearOtherModeSelections(MeasurementMode.VERTICAL_LINE)
                        activeMeasurementMode = MeasurementMode.VERTICAL_LINE
                        updateMixedOverlayDisplay()
                    }
                    Log.d(TAG, "🎯 Delegating touch to VerticalLineMeasureHelper for vertical line measurement")
                    handled = verticalLineMeasureHelper.handleTouchEvent(event, viewWidth, viewHeight)
                    Log.d(TAG, "🎯 VerticalLineMeasureHelper.handleTouchEvent returned: $handled")
                }
                // 然后检查平行线测量
                else if (isParallelLinesMeasuring && parallelLinesMeasureHelper.isNearAnyMeasurement(touchPoint)) {
                    if (activeMeasurementMode != MeasurementMode.PARALLEL_LINES) {
                        Log.d(TAG, "🎯 Touch detected on parallel lines measurement - auto-activating PARALLEL_LINES mode")
                        // 🔄 清除其他模式的选中状态
                        clearOtherModeSelections(MeasurementMode.PARALLEL_LINES)
                        activeMeasurementMode = MeasurementMode.PARALLEL_LINES
                        updateMixedOverlayDisplay()
                    }
                    Log.d(TAG, "🎯 Delegating touch to ParallelLinesMeasureHelper for parallel lines measurement")
                    handled = parallelLinesMeasureHelper.handleTouchEvent(event, viewWidth, viewHeight)
                    Log.d(TAG, "🎯 ParallelLinesMeasureHelper.handleTouchEvent returned: $handled")
                }
                // 然后检查三垂直测量
                else if (isThreeVerticalMeasuring && threeVerticalMeasureHelper.isNearAnyMeasurement(touchPoint)) {
                    if (activeMeasurementMode != MeasurementMode.THREE_VERTICAL) {
                        Log.d(TAG, "🎯 Touch detected on three vertical measurement - auto-activating THREE_VERTICAL mode")
                        // 🔄 清除其他模式的选中状态
                        clearOtherModeSelections(MeasurementMode.THREE_VERTICAL)
                        activeMeasurementMode = MeasurementMode.THREE_VERTICAL
                        updateMixedOverlayDisplay()
                    }
                    Log.d(TAG, "🎯 Delegating touch to ThreeVerticalMeasureHelper for three vertical measurement")
                    handled = threeVerticalMeasureHelper.handleTouchEvent(event, viewWidth, viewHeight)
                    Log.d(TAG, "🎯 ThreeVerticalMeasureHelper.handleTouchEvent returned: $handled")
                }
                // 然后检查矩形测量
                else if (isRectangleMeasuring && rectangleMeasureHelper.isNearAnyMeasurement(touchPoint)) {
                    if (activeMeasurementMode != MeasurementMode.RECTANGLE) {
                        Log.d(TAG, "🎯 Touch detected on rectangle measurement - auto-activating RECTANGLE mode")
                        // 🔄 清除其他模式的选中状态
                        clearOtherModeSelections(MeasurementMode.RECTANGLE)
                        activeMeasurementMode = MeasurementMode.RECTANGLE
                        updateMixedOverlayDisplay()
                    }
                    Log.d(TAG, "🎯 Delegating touch to RectangleMeasureHelper for rectangle measurement")
                    handled = rectangleMeasureHelper.handleTouchEvent(event, viewWidth, viewHeight)
                    Log.d(TAG, "🎯 RectangleMeasureHelper.handleTouchEvent returned: $handled")
                }
                // 然后检查椭圆测量
                else if (isEllipseMeasuring && ellipseMeasureHelper.isNearAnyMeasurement(touchPoint)) {
                    if (activeMeasurementMode != MeasurementMode.ELLIPSE) {
                        Log.d(TAG, "🎯 Touch detected on ellipse measurement - auto-activating ELLIPSE mode")
                        // 🔄 清除其他模式的选中状态
                        clearOtherModeSelections(MeasurementMode.ELLIPSE)
                        activeMeasurementMode = MeasurementMode.ELLIPSE
                        updateMixedOverlayDisplay()
                    }
                    Log.d(TAG, "🎯 Delegating touch to EllipseMeasureHelper for ellipse measurement")
                    handled = ellipseMeasureHelper.handleTouchEvent(event, viewWidth, viewHeight)
                    Log.d(TAG, "🎯 EllipseMeasureHelper.handleTouchEvent returned: $handled")
                }
                // 然后检查三点矩形测量
                else if (isThreeRectangleMeasuring && threeRectangleMeasureHelper.isNearAnyMeasurement(touchPoint)) {
                    if (activeMeasurementMode != MeasurementMode.THREE_RECTANGLE) {
                        Log.d(TAG, "🎯 Touch detected on three rectangle measurement - auto-activating THREE_RECTANGLE mode")
                        // 🔄 清除其他模式的选中状态
                        clearOtherModeSelections(MeasurementMode.THREE_RECTANGLE)
                        activeMeasurementMode = MeasurementMode.THREE_RECTANGLE
                        updateMixedOverlayDisplay()
                    }
                    Log.d(TAG, "🎯 Delegating touch to ThreeRectangleMeasureHelper for three rectangle measurement")
                    handled = threeRectangleMeasureHelper.handleTouchEvent(event, viewWidth, viewHeight)
                    Log.d(TAG, "🎯 ThreeRectangleMeasureHelper.handleTouchEvent returned: $handled")
                }
                // 如果没有触摸到任何测量，检查是否为空白区域点击
                else {
                    Log.d(TAG, "🔍 No measurement touched - checking for empty area click")
                    if (event.action == MotionEvent.ACTION_UP) {
                        Log.d(TAG, "🔍 ACTION_UP detected - checking if near any measurement")
                        // 检查是否点击了空白区域（远离所有测量）
                        val isNearAnyMeasurement =
                            (isAngleMeasuring && angleMeasureHelper.isNearAnyMeasurement(touchPoint)) ||
                            (isFourPointAngleMeasuring && fourPointAngleHelper.isNearAnyMeasurement(touchPoint)) ||
                            (isPointMeasuring && pointMeasureHelper.isNearAnyMeasurement(touchPoint)) ||
                            (isLineMeasuring && lineMeasureHelper.isNearAnyMeasurement(touchPoint)) ||
                            (isHorizonLineMeasuring && horizonLineMeasureHelper.isNearAnyMeasurement(touchPoint)) ||
                            (isVerticalLineMeasuring && verticalLineMeasureHelper.isNearAnyMeasurement(touchPoint)) ||
                            (isParallelLinesMeasuring && parallelLinesMeasureHelper.isNearAnyMeasurement(touchPoint)) ||
                            (isThreeVerticalMeasuring && threeVerticalMeasureHelper.isNearAnyMeasurement(touchPoint)) ||
                            (isRectangleMeasuring && rectangleMeasureHelper.isNearAnyMeasurement(touchPoint)) ||
                            (isThreeRectangleMeasuring && threeRectangleMeasureHelper.isNearAnyMeasurement(touchPoint)) ||
                            (isEllipseMeasuring && ellipseMeasureHelper.isNearAnyMeasurement(touchPoint))

                        Log.d(TAG, "🔍 isNearAnyMeasurement: $isNearAnyMeasurement (angle: $isAngleMeasuring, fourPoint: $isFourPointAngleMeasuring, point: $isPointMeasuring, line: $isLineMeasuring, horizonLine: $isHorizonLineMeasuring, verticalLine: $isVerticalLineMeasuring, parallelLines: $isParallelLinesMeasuring, threeVertical: $isThreeVerticalMeasuring)")

                        if (!isNearAnyMeasurement) {
                            Log.d(TAG, "🔄 Clicked on empty area - clearing all selections")
                            // 清除所有选中状态
                            if (isAngleMeasuring) {
                                Log.d(TAG, "🔄 Clearing angle measurement selection")
                                angleMeasureHelper.clearSelection()
                            }
                            if (isFourPointAngleMeasuring) {
                                Log.d(TAG, "🔄 Clearing four-point angle measurement selection")
                                fourPointAngleHelper.clearSelection()
                            }
                            if (isPointMeasuring) {
                                Log.d(TAG, "🔄 Clearing point measurement selection")
                                pointMeasureHelper.clearSelection()
                            }
                            if (isLineMeasuring) {
                                Log.d(TAG, "🔄 Clearing line measurement selection")
                                lineMeasureHelper.clearSelection()
                            }
                            if (isHorizonLineMeasuring) {
                                Log.d(TAG, "🔄 Clearing horizon line measurement selection")
                                horizonLineMeasureHelper.clearSelection()
                            }
                            if (isVerticalLineMeasuring) {
                                Log.d(TAG, "🔄 Clearing vertical line measurement selection")
                                verticalLineMeasureHelper.clearSelection()
                            }
                            if (isParallelLinesMeasuring) {
                                Log.d(TAG, "🔄 Clearing parallel lines measurement selection")
                                parallelLinesMeasureHelper.clearSelection()
                            }
                            if (isThreeVerticalMeasuring) {
                                Log.d(TAG, "🔄 Clearing three vertical measurement selection")
                                threeVerticalMeasureHelper.clearSelection()
                            }
                            if (isRectangleMeasuring) {
                                Log.d(TAG, "🔄 Clearing rectangle measurement selection")
                                rectangleMeasureHelper.clearSelection()
                            }
                            if (isThreeRectangleMeasuring) {
                                Log.d(TAG, "🔄 Clearing three rectangle measurement selection")
                                threeRectangleMeasureHelper.clearSelection()
                            }
                            activeMeasurementMode = null
                            updateMixedOverlayDisplay()
                            handled = false // 不消费事件
                        } else {
                            Log.d(TAG, "🔍 Click is near measurement - keeping current selection")
                        }
                    }

                    // 如果不是空白区域点击，使用当前激活的模式处理
                    if (!handled) {
                        when (activeMeasurementMode) {
                            MeasurementMode.ANGLE -> {
                                if (isAngleMeasuring) {
                                    handled = angleMeasureHelper.handleTouchEvent(event, viewWidth, viewHeight)
                                }
                            }
                            MeasurementMode.FOUR_POINT_ANGLE -> {
                                if (isFourPointAngleMeasuring) {
                                    handled = fourPointAngleHelper.handleTouchEvent(event, viewWidth, viewHeight)
                                }
                            }
                            MeasurementMode.POINT -> {
                                if (isPointMeasuring) {
                                    handled = pointMeasureHelper.handleTouchEvent(event, viewWidth, viewHeight)
                                }
                            }
                            MeasurementMode.LINE -> {
                                if (isLineMeasuring) {
                                    handled = lineMeasureHelper.handleTouchEvent(event, viewWidth, viewHeight)
                                }
                            }
                            MeasurementMode.HORIZON_LINE -> {
                                if (isHorizonLineMeasuring) {
                                    handled = horizonLineMeasureHelper.handleTouchEvent(event, viewWidth, viewHeight)
                                }
                            }
                            MeasurementMode.VERTICAL_LINE -> {
                                if (isVerticalLineMeasuring) {
                                    handled = verticalLineMeasureHelper.handleTouchEvent(event, viewWidth, viewHeight)
                                }
                            }
                            MeasurementMode.PARALLEL_LINES -> {
                                if (isParallelLinesMeasuring) {
                                    handled = parallelLinesMeasureHelper.handleTouchEvent(event, viewWidth, viewHeight)
                                }
                            }
                            MeasurementMode.THREE_VERTICAL -> {
                                if (isThreeVerticalMeasuring) {
                                    handled = threeVerticalMeasureHelper.handleTouchEvent(event, viewWidth, viewHeight)
                                }
                            }
                            MeasurementMode.RECTANGLE -> {
                                if (isRectangleMeasuring) {
                                    handled = rectangleMeasureHelper.handleTouchEvent(event, viewWidth, viewHeight)
                                }
                            }
                            MeasurementMode.THREE_RECTANGLE -> {
                                if (isThreeRectangleMeasuring) {
                                    handled = threeRectangleMeasureHelper.handleTouchEvent(event, viewWidth, viewHeight)
                                }
                            }
                            MeasurementMode.ELLIPSE -> {
                                if (isEllipseMeasuring) {
                                    handled = ellipseMeasureHelper.handleTouchEvent(event, viewWidth, viewHeight)
                                }
                            }
                            null -> {
                                // 静默处理
                            }
                        }
                    }
                }
            } else {
                // 对于非ACTION_DOWN事件，也需要检查空白区域点击
                val touchPoint = android.graphics.PointF(event.x, event.y)

                // 检查是否触摸到任何测量
                val touchedAngleMeasurement = isAngleMeasuring && angleMeasureHelper.isPointOnMeasurement(touchPoint)
                val touchedFourPointMeasurement = isFourPointAngleMeasuring && fourPointAngleHelper.isPointOnMeasurement(touchPoint)
                val touchedPointMeasurement = isPointMeasuring && pointMeasureHelper.isPointOnMeasurement(touchPoint)
                val touchedLineMeasurement = isLineMeasuring && lineMeasureHelper.isPointOnMeasurement(touchPoint)
                val touchedHorizonLineMeasurement = isHorizonLineMeasuring && horizonLineMeasureHelper.isPointOnMeasurement(touchPoint)
                val touchedVerticalLineMeasurement = isVerticalLineMeasuring && verticalLineMeasureHelper.isPointOnMeasurement(touchPoint)
                val touchedParallelLinesMeasurement = isParallelLinesMeasuring && parallelLinesMeasureHelper.isPointOnMeasurement(touchPoint)
                val touchedThreeVerticalMeasurement = isThreeVerticalMeasuring && threeVerticalMeasureHelper.isPointOnMeasurement(touchPoint)
                val touchedRectangleMeasurement = isRectangleMeasuring && rectangleMeasureHelper.isNearAnyMeasurement(touchPoint)
                val touchedThreeRectangleMeasurement = isThreeRectangleMeasuring && threeRectangleMeasureHelper.isNearAnyMeasurement(touchPoint)
                val touchedEllipseMeasurement = isEllipseMeasuring && ellipseMeasureHelper.isNearAnyMeasurement(touchPoint)

                if (touchedAngleMeasurement) {
                    Log.d(TAG, "🎯 Non-ACTION_DOWN: Touched angle measurement")
                    if (isAngleMeasuring) {
                        handled = angleMeasureHelper.handleTouchEvent(event, viewWidth, viewHeight)
                    }
                } else if (touchedFourPointMeasurement) {
                    Log.d(TAG, "🎯 Non-ACTION_DOWN: Touched four-point measurement")
                    if (isFourPointAngleMeasuring) {
                        handled = fourPointAngleHelper.handleTouchEvent(event, viewWidth, viewHeight)
                    }
                } else if (touchedPointMeasurement) {
                    Log.d(TAG, "🎯 Non-ACTION_DOWN: Touched point measurement")
                    if (isPointMeasuring) {
                        handled = pointMeasureHelper.handleTouchEvent(event, viewWidth, viewHeight)
                    }
                } else if (touchedLineMeasurement) {
                    Log.d(TAG, "🎯 Non-ACTION_DOWN: Touched line measurement")
                    if (isLineMeasuring) {
                        handled = lineMeasureHelper.handleTouchEvent(event, viewWidth, viewHeight)
                    }
                } else if (touchedHorizonLineMeasurement) {
                    Log.d(TAG, "🎯 Non-ACTION_DOWN: Touched horizon line measurement")
                    if (isHorizonLineMeasuring) {
                        handled = horizonLineMeasureHelper.handleTouchEvent(event, viewWidth, viewHeight)
                    }
                } else if (touchedVerticalLineMeasurement) {
                    Log.d(TAG, "🎯 Non-ACTION_DOWN: Touched vertical line measurement")
                    if (isVerticalLineMeasuring) {
                        handled = verticalLineMeasureHelper.handleTouchEvent(event, viewWidth, viewHeight)
                    }
                } else if (touchedParallelLinesMeasurement) {
                    Log.d(TAG, "🎯 Non-ACTION_DOWN: Touched parallel lines measurement")
                    if (isParallelLinesMeasuring) {
                        handled = parallelLinesMeasureHelper.handleTouchEvent(event, viewWidth, viewHeight)
                    }
                }else if (touchedThreeVerticalMeasurement){
                    Log.d(TAG, "🎯 Non-ACTION_DOWN: Touched three vertical measurement")
                    if (isThreeVerticalMeasuring){
                        handled = threeVerticalMeasureHelper.handleTouchEvent(event, viewWidth, viewHeight)
                    }
                } else if (touchedRectangleMeasurement) {
                    Log.d(TAG, "🎯 Non-ACTION_DOWN: Touched rectangle measurement")
                    if (isRectangleMeasuring) {
                        handled = rectangleMeasureHelper.handleTouchEvent(event, viewWidth, viewHeight)
                    }
                } else if (touchedThreeRectangleMeasurement) {
                    Log.d(TAG, "🎯 Non-ACTION_DOWN: Touched three rectangle measurement")
                    if (isThreeRectangleMeasuring) {
                        handled = threeRectangleMeasureHelper.handleTouchEvent(event, viewWidth, viewHeight)
                    }
                } else if (touchedEllipseMeasurement) {
                    Log.d(TAG, "🎯 Non-ACTION_DOWN: Touched ellipse measurement")
                    if (isEllipseMeasuring) {
                        handled = ellipseMeasureHelper.handleTouchEvent(event, viewWidth, viewHeight)
                    }
                } else {
                    // 没有触摸到任何测量，检查是否为空白区域点击
                    if (event.action == MotionEvent.ACTION_UP) {
                        Log.d(TAG, "🔍 Non-ACTION_DOWN: ACTION_UP detected - checking if near any measurement")
                        val isNearAnyMeasurement =
                            (isAngleMeasuring && angleMeasureHelper.isNearAnyMeasurement(touchPoint)) ||
                            (isFourPointAngleMeasuring && fourPointAngleHelper.isNearAnyMeasurement(touchPoint)) ||
                            (isPointMeasuring && pointMeasureHelper.isNearAnyMeasurement(touchPoint)) ||
                            (isLineMeasuring && lineMeasureHelper.isNearAnyMeasurement(touchPoint)) ||
                            (isHorizonLineMeasuring && horizonLineMeasureHelper.isNearAnyMeasurement(touchPoint)) ||
                            (isVerticalLineMeasuring && verticalLineMeasureHelper.isNearAnyMeasurement(touchPoint)) ||
                            (isParallelLinesMeasuring && parallelLinesMeasureHelper.isNearAnyMeasurement(touchPoint)) ||
                            (isThreeVerticalMeasuring && threeVerticalMeasureHelper.isNearAnyMeasurement(touchPoint)) ||
                            (isRectangleMeasuring && rectangleMeasureHelper.isNearAnyMeasurement(touchPoint)) ||
                            (isThreeRectangleMeasuring && threeRectangleMeasureHelper.isNearAnyMeasurement(touchPoint)) ||
                            (isEllipseMeasuring && ellipseMeasureHelper.isNearAnyMeasurement(touchPoint))

                        Log.d(TAG, "🔍 Non-ACTION_DOWN: isNearAnyMeasurement: $isNearAnyMeasurement")

                        if (!isNearAnyMeasurement) {
                            Log.d(TAG, "🔄 Non-ACTION_DOWN: Clicked on empty area - clearing all selections")
                            // 清除所有选中状态
                            if (isAngleMeasuring) {
                                Log.d(TAG, "🔄 Non-ACTION_DOWN: Clearing angle measurement selection")
                                angleMeasureHelper.clearSelection()
                            }
                            if (isFourPointAngleMeasuring) {
                                Log.d(TAG, "🔄 Non-ACTION_DOWN: Clearing four-point angle measurement selection")
                                fourPointAngleHelper.clearSelection()
                            }
                            if (isPointMeasuring) {
                                Log.d(TAG, "🔄 Non-ACTION_DOWN: Clearing point measurement selection")
                                pointMeasureHelper.clearSelection()
                            }
                            if (isLineMeasuring) {
                                Log.d(TAG, "🔄 Non-ACTION_DOWN: Clearing line measurement selection")
                                lineMeasureHelper.clearSelection()
                            }
                            if (isHorizonLineMeasuring) {
                                Log.d(TAG, "🔄 Non-ACTION_DOWN: Clearing horizon line measurement selection")
                                horizonLineMeasureHelper.clearSelection()
                            }
                            if (isVerticalLineMeasuring) {
                                Log.d(TAG, "🔄 Non-ACTION_DOWN: Clearing vertical line measurement selection")
                                verticalLineMeasureHelper.clearSelection()
                            }
                            if (isParallelLinesMeasuring) {
                                Log.d(TAG, "🔄 Non-ACTION_DOWN: Clearing parallel lines measurement selection")
                                parallelLinesMeasureHelper.clearSelection()
                            }
                            if (isThreeVerticalMeasuring) {
                                Log.d(TAG, "🔄 Non-ACTION_DOWN: Clearing three vertical measurement selection")
                                threeVerticalMeasureHelper.clearSelection()
                            }
                            if (isRectangleMeasuring) {
                                Log.d(TAG, "🔄 Non-ACTION_DOWN: Clearing rectangle measurement selection")
                                rectangleMeasureHelper.clearSelection()
                            }
                            if (isThreeRectangleMeasuring) {
                                Log.d(TAG, "🔄 Non-ACTION_DOWN: Clearing three rectangle measurement selection")
                                threeRectangleMeasureHelper.clearSelection()
                            }
                            activeMeasurementMode = null
                            updateMixedOverlayDisplay()
                            handled = false // 不消费事件
                        }
                    } else {
                        // 其他非ACTION_UP事件，使用当前激活的模式处理
                        when (activeMeasurementMode) {
                            MeasurementMode.ANGLE -> {
                                if (isAngleMeasuring) {
                                    handled = angleMeasureHelper.handleTouchEvent(event, viewWidth, viewHeight)
                                }
                            }
                            MeasurementMode.FOUR_POINT_ANGLE -> {
                                if (isFourPointAngleMeasuring) {
                                    handled = fourPointAngleHelper.handleTouchEvent(event, viewWidth, viewHeight)
                                }
                            }
                            MeasurementMode.POINT -> {
                                if (isPointMeasuring) {
                                    handled = pointMeasureHelper.handleTouchEvent(event, viewWidth, viewHeight)
                                }
                            }
                            MeasurementMode.LINE -> {
                                if (isLineMeasuring) {
                                    handled = lineMeasureHelper.handleTouchEvent(event, viewWidth, viewHeight)
                                }
                            }
                            MeasurementMode.HORIZON_LINE -> {
                                if (isHorizonLineMeasuring) {
                                    handled = horizonLineMeasureHelper.handleTouchEvent(event, viewWidth, viewHeight)
                                }
                            }
                            MeasurementMode.VERTICAL_LINE -> {
                                if (isVerticalLineMeasuring) {
                                    handled = verticalLineMeasureHelper.handleTouchEvent(event, viewWidth, viewHeight)
                                }
                            }
                            MeasurementMode.PARALLEL_LINES -> {
                                if (isParallelLinesMeasuring) {
                                    handled = parallelLinesMeasureHelper.handleTouchEvent(event, viewWidth, viewHeight)
                                }
                            }
                            MeasurementMode.THREE_VERTICAL -> {
                                if (isThreeVerticalMeasuring) {
                                    handled = threeVerticalMeasureHelper.handleTouchEvent(event, viewWidth, viewHeight)
                                }
                            }
                            MeasurementMode.RECTANGLE -> {
                                if (isRectangleMeasuring) {
                                    handled = rectangleMeasureHelper.handleTouchEvent(event, viewWidth, viewHeight)
                                }
                            }
                            MeasurementMode.THREE_RECTANGLE -> {
                                if (isThreeRectangleMeasuring) {
                                    handled = threeRectangleMeasureHelper.handleTouchEvent(event, viewWidth, viewHeight)
                                }
                            }
                            MeasurementMode.ELLIPSE -> {
                                if (isEllipseMeasuring) {
                                    handled = ellipseMeasureHelper.handleTouchEvent(event, viewWidth, viewHeight)
                                }
                            }
                            null -> {
                                // 静默处理
                            }
                        }
                    }
                }
            }

            handled
        }

        // 将混合测量处理器设置到TpImageView
        imageView.setMeasurementTouchHandler(mixedMeasurementHandler)

        Log.d(TAG, "🚀 Smart mixed touch handler setup complete - active mode: $activeMeasurementMode")
    }

    /**
     * 🔍 检查是否有有效的触摸处理器
     */
    private fun hasValidTouchHandler(): Boolean {
        // 简单检查：如果任一测量模式激活，认为已有有效处理器
        return isAngleMeasuring || isFourPointAngleMeasuring || isPointMeasuring || isLineMeasuring || isHorizonLineMeasuring || isRectangleMeasuring || isThreeRectangleMeasuring
    }

    /**
     * 📊 状态验证方法 - 确保状态一致性（支持混合共存模式）
     */
    private fun validateState(): Boolean {
        val activeModeCount = listOf(
            isAngleMeasuring, isFourPointAngleMeasuring, isPointMeasuring,
            isLineMeasuring, isHorizonLineMeasuring, isVerticalLineMeasuring,
            isParallelLinesMeasuring, isThreeVerticalMeasuring, isRectangleMeasuring
        ).count { it }

        val isValid = when {
            activeModeCount == 0 -> {
                Log.d(TAG, "✅ Valid state: No measurement mode active")
                true
            }
            activeModeCount == 1 -> {
                Log.d(TAG, "✅ Valid state: Single measurement mode active (angle=$isAngleMeasuring, fourPoint=$isFourPointAngleMeasuring, point=$isPointMeasuring, line=$isLineMeasuring, horizonLine=$isHorizonLineMeasuring, verticalLine=$isVerticalLineMeasuring, parallelLines=$isParallelLinesMeasuring, threeVertical=$isThreeVerticalMeasuring)")
                true
            }
            activeModeCount > 1 -> {
                // 混合共存模式：多种测量都可以存在，但需要有明确的激活状态
                val hasActiveMode = activeMeasurementMode != null
                if (hasActiveMode) {
                    Log.d(TAG, "✅ Valid state: Mixed coexistence mode (active: $activeMeasurementMode, modes: angle=$isAngleMeasuring, fourPoint=$isFourPointAngleMeasuring, point=$isPointMeasuring, line=$isLineMeasuring, horizonLine=$isHorizonLineMeasuring, verticalLine=$isVerticalLineMeasuring, parallelLines=$isParallelLinesMeasuring, threeVertical=$isThreeVerticalMeasuring)")
                    true
                } else {
                    Log.e(TAG, "❌ Invalid state: Mixed mode without active measurement type")
                    false
                }
            }
            else -> {
                Log.e(TAG, "❌ Invalid state: Unexpected condition")
                false
            }
        }
        return isValid
    }

    /**
     * 🔒 保存当前选中状态 - 用于删除操作保护
     */
    private fun saveCurrentSelectionState(): SelectionState {
        return SelectionState(
            activeMeasurementMode = activeMeasurementMode,
            angleSelectedId = if (isAngleMeasuring && angleMeasureHelper.hasSelectedMeasurement()) "angle_selected" else null,
            fourPointAngleSelectedId = if (isFourPointAngleMeasuring && fourPointAngleHelper.hasSelectedMeasurement()) "fourpoint_selected" else null,
            pointSelectedId = if (isPointMeasuring && pointMeasureHelper.hasSelectedMeasurement()) "point_selected" else null,
            lineSelectedId = if (isLineMeasuring && lineMeasureHelper.hasSelectedMeasurement()) "line_selected" else null,
            horizonLineSelectedId = if (isHorizonLineMeasuring && horizonLineMeasureHelper.hasSelectedMeasurement()) "horizon_selected" else null,
            verticalLineSelectedId = if (isVerticalLineMeasuring && verticalLineMeasureHelper.hasSelectedMeasurement()) "vertical_selected" else null,
            parallelLinesSelectedId = if (isParallelLinesMeasuring && parallelLinesMeasureHelper.hasSelectedMeasurement()) "parallel_selected" else null,
            threeVerticalSelectedId = if (isThreeVerticalMeasuring && threeVerticalMeasureHelper.hasSelectedMeasurement()) "threev_selected" else null
        )
    }

    /**
     * 🔧 恢复选中状态 - 用于删除操作保护（简化版本）
     */
    private fun restoreSelectionState(savedState: SelectionState): Boolean {
        var restored = false

        try {
            // 简化恢复逻辑：如果保存的状态显示某个模式有选中测量，强制选中最后一个测量
            savedState.parallelLinesSelectedId?.let {
                if (isParallelLinesMeasuring && parallelLinesMeasureHelper.getMeasurementCount() > 0) {
                    val measurements = parallelLinesMeasureHelper.getAllMeasurements()
                    measurements.lastOrNull()?.let { lastMeasurement ->
                        measurements.forEach { it.isSelected = false }
                        lastMeasurement.isSelected = true
                        parallelLinesMeasureHelper.setSelectedMeasurement(lastMeasurement)
                        restored = true
                        Log.d(TAG, "🔧 Restored parallel lines measurement selection: ${lastMeasurement.id}")
                    }
                }
            }

            savedState.threeVerticalSelectedId?.let {
                if (isThreeVerticalMeasuring && threeVerticalMeasureHelper.getMeasurementCount() > 0) {
                    val measurements = threeVerticalMeasureHelper.getAllMeasurements()
                    measurements.lastOrNull()?.let { lastMeasurement ->
                        measurements.forEach { it.isSelected = false }
                        lastMeasurement.isSelected = true
                        threeVerticalMeasureHelper.setSelectedMeasurement(lastMeasurement)
                        restored = true
                        Log.d(TAG, "🔧 Restored three vertical measurement selection: ${lastMeasurement.id}")
                    }
                }
            }

            // 可以为其他测量类型添加类似的恢复逻辑

        } catch (e: Exception) {
            Log.e(TAG, "❌ Failed to restore selection state: ${e.message}")
        }

        return restored
    }

    /**
     * 📊 获取当前测量状态
     */
    fun isAngleMeasuring(): Boolean = isAngleMeasuring

    /**
     * 📊 获取四点角度测量状态
     */
    fun isFourPointAngleMeasuring(): Boolean = isFourPointAngleMeasuring

    /**
     * 📊 获取水平线测量状态
     */
    fun isHorizonLineMeasuring(): Boolean = isHorizonLineMeasuring

    /**
     * 📊 检查是否在垂直线测量模式下
     */
    fun isVerticalLineMeasuring(): Boolean = isVerticalLineMeasuring

    /**
     * 📊 检查是否在任何测量模式下（混合模式支持）
     */
    fun isMixedMeasuring(): Boolean = isAngleMeasuring || isFourPointAngleMeasuring || isPointMeasuring || isLineMeasuring || isHorizonLineMeasuring || isVerticalLineMeasuring || isParallelLinesMeasuring || isThreeVerticalMeasuring || isRectangleMeasuring || isThreeRectangleMeasuring || isEllipseMeasuring

    /**
     * 🔄 设置统一的缩放监听器 - 解决监听器相互覆盖问题
     */
    private fun setupUnifiedScaleListener() {
        Log.d(TAG, "🔄 Setting up unified scale listener for all measurement types")

        imageView.setMatrixChangeListener {
            Log.d(TAG, "🔄 [UNIFIED] Matrix changed - updating all active measurements")

            if (isAngleMeasuring) {
                Log.d(TAG, "🔄 [UNIFIED] Calling angleMeasureHelper.onScaleChanged()")
                angleMeasureHelper.onScaleChanged()
            }
            if (isFourPointAngleMeasuring) {
                Log.d(TAG, "🔄 [UNIFIED] Calling fourPointAngleHelper.onScaleChanged()")
                fourPointAngleHelper.onScaleChanged()
            }
            if (isPointMeasuring) {
                Log.d(TAG, "🔄 [UNIFIED] Calling pointMeasureHelper.onScaleChanged()")
                pointMeasureHelper.onScaleChanged()
            }
            if (isLineMeasuring) {
                Log.d(TAG, "🔄 [UNIFIED] Calling lineMeasureHelper.onScaleChanged()")
                lineMeasureHelper.onScaleChanged()
            }
            if (isHorizonLineMeasuring) {
                Log.d(TAG, "🔄 [UNIFIED] Calling horizonLineMeasureHelper.onScaleChanged()")
                horizonLineMeasureHelper.onScaleChanged()
            }
            if (isVerticalLineMeasuring) {
                Log.d(TAG, "🔄 [UNIFIED] Calling verticalLineMeasureHelper.onScaleChanged()")
                verticalLineMeasureHelper.onScaleChanged()
            }
            if (isParallelLinesMeasuring) {
                Log.d(TAG, "🔄 [UNIFIED] Calling parallelLinesMeasureHelper.onScaleChanged()")
                parallelLinesMeasureHelper.onScaleChanged()
            }
            if (isThreeVerticalMeasuring) {
                Log.d(TAG, "🔄 [UNIFIED] Calling threeVerticalMeasureHelper.onScaleChanged()")
                threeVerticalMeasureHelper.onScaleChanged()
            }
            if (isRectangleMeasuring) {
                Log.d(TAG, "🔄 [UNIFIED] Calling rectangleMeasureHelper.onScaleChanged()")
                rectangleMeasureHelper.onScaleChanged()
            }
            if (isThreeRectangleMeasuring) {
                Log.d(TAG, "🔄 [UNIFIED] Calling threeRectangleMeasureHelper.onScaleChanged()")
                threeRectangleMeasureHelper.onScaleChanged()
            }
            if (isEllipseMeasuring) {
                Log.d(TAG, "🔄 [UNIFIED] Calling ellipseMeasureHelper.onScaleChanged()")
                ellipseMeasureHelper.onScaleChanged()
            }

            updateMixedOverlayDisplay()
        }

        Log.d(TAG, "✅ Unified scale listener setup completed")
    }

    /**
     * 🔄 设置激活的测量模式
     */
    private fun setActiveMode(mode: MeasurementMode) {
        activeMeasurementMode = mode
        Log.d(TAG, "🔄 Active measurement mode set to: $mode")

        // 更新触摸处理器以响应新的激活模式
        setupMixedTouchHandler()

        // 更新覆盖层显示
        updateMixedOverlayDisplay()
    }

    /**
     * 🔄 取消激活三点角度测量（保持数据，仅改变激活状态）
     */
    fun deactivateAngleMeasurement() {
        if (isAngleMeasuring) {
            Log.d(TAG, "🔄 Deactivating angle measurement, keeping data but clearing selection")
            // 清除三点角度测量的选中状态
            angleMeasureHelper.clearSelection()
            // 不改变 isAngleMeasuring 状态，不改变 activeMeasurementMode
            // 让新的激活方法来设置正确的激活模式
            Log.d(TAG, "🔄 Current activeMeasurementMode: $activeMeasurementMode (will be overridden by new activation)")
            updateMixedOverlayDisplay()
        }
    }

    /**
     * 🔄 取消激活四点角度测量（保持数据，仅改变激活状态）
     */
    fun deactivateFourPointAngleMeasurement() {
        if (isFourPointAngleMeasuring) {
            Log.d(TAG, "🔄 Deactivating four-point angle measurement, keeping data but clearing selection")
            // 清除四点角度测量的选中状态
            fourPointAngleHelper.clearSelection()
            // 不改变 isFourPointAngleMeasuring 状态，不改变 activeMeasurementMode
            // 让新的激活方法来设置正确的激活模式
            Log.d(TAG, "🔄 Current activeMeasurementMode: $activeMeasurementMode (will be overridden by new activation)")
            updateMixedOverlayDisplay()
        }
    }

    /**
     * � 取消激活点测量（保持数据，仅改变激活状态）
     */
    fun deactivatePointMeasurement() {
        if (isPointMeasuring) {
            Log.d(TAG, "🔄 Deactivating point measurement, keeping data but clearing selection")
            // 清除点测量的选中状态
            pointMeasureHelper.clearSelection()
            // 不改变 isPointMeasuring 状态，不改变 activeMeasurementMode
            // 让新的激活方法来设置正确的激活模式
            Log.d(TAG, "🔄 Current activeMeasurementMode: $activeMeasurementMode (will be overridden by new activation)")
            updateMixedOverlayDisplay()
        }
    }

    /**
     * 🔄 取消激活线段测量（保持数据，仅改变激活状态）
     */
    fun deactivateLineMeasurement() {
        if (isLineMeasuring) {
            Log.d(TAG, "🔄 Deactivating line measurement, keeping data but clearing selection")
            // 清除线段测量的选中状态
            lineMeasureHelper.clearSelection()
            // 不改变 isLineMeasuring 状态，不改变 activeMeasurementMode
            // 让新的激活方法来设置正确的激活模式
            Log.d(TAG, "🔄 Current activeMeasurementMode: $activeMeasurementMode (will be overridden by new activation)")
            updateMixedOverlayDisplay()
        }
    }

    /**
     * 🔄 取消激活水平线测量（保持数据，仅改变激活状态）
     */
    fun deactivateHorizonLineMeasurement() {
        if (isHorizonLineMeasuring) {
            Log.d(TAG, "🔄 Deactivating horizon line measurement, keeping data but clearing selection")
            // 清除水平线测量的选中状态
            horizonLineMeasureHelper.clearSelection()
            // 不改变 isHorizonLineMeasuring 状态，不改变 activeMeasurementMode
            // 让新的激活方法来设置正确的激活模式
            Log.d(TAG, "🔄 Current activeMeasurementMode: $activeMeasurementMode (will be overridden by new activation)")
            updateMixedOverlayDisplay()
        }
    }

    /**
     * �📊 获取当前激活的测量模式
     */
    fun getActiveMeasurementMode(): MeasurementMode? = activeMeasurementMode

    /**
     * 📊 检查指定模式是否为激活状态
     */
    fun isActiveMeasurementMode(mode: MeasurementMode): Boolean = activeMeasurementMode == mode

    /**
     * 🔄 清除其他模式的选中状态（避免多模式选中冲突）
     */
    private fun clearOtherModeSelections(activeMode: MeasurementMode) {
        when (activeMode) {
            MeasurementMode.ANGLE -> {
                // 清除四点角度测量的选中状态
                if (isFourPointAngleMeasuring) {
                    fourPointAngleHelper.clearSelection()
                    Log.d(TAG, "🔄 Cleared four-point angle measurement selections")
                }
                // 清除点测量的选中状态
                if (isPointMeasuring) {
                    pointMeasureHelper.clearSelection()
                    Log.d(TAG, "🔄 Cleared point measurement selections")
                }
                // 清除线段测量的选中状态
                if (isLineMeasuring) {
                    lineMeasureHelper.clearSelection()
                    Log.d(TAG, "🔄 Cleared line measurement selections")
                }
                // 清除水平线测量的选中状态
                if (isHorizonLineMeasuring) {
                    horizonLineMeasureHelper.clearSelection()
                    Log.d(TAG, "🔄 Cleared horizon line measurement selections")
                }
                // 清除垂直线测量的选中状态
                if (isVerticalLineMeasuring) {
                    verticalLineMeasureHelper.clearSelection()
                    Log.d(TAG, "🔄 Cleared vertical line measurement selections")
                }
                //清除平行线
                if (isParallelLinesMeasuring){
                    parallelLinesMeasureHelper.clearSelection()
                    Log.d(TAG, "🔄 Cleared parallel lines measurement selections")
                }
                //三线
                if(isThreeVerticalMeasuring){
                    threeVerticalMeasureHelper.clearSelection()
                    Log.d(TAG, "🔄 Cleared three-vertical measurement selections")
                }
                // 清除矩形测量的选中状态
                if (isRectangleMeasuring) {
                    rectangleMeasureHelper.clearSelection()
                    Log.d(TAG, "🔄 Cleared rectangle measurement selections")
                }
                // 清除三点矩形测量的选中状态
                if (isThreeRectangleMeasuring) {
                    threeRectangleMeasureHelper.clearSelection()
                    Log.d(TAG, "🔄 Cleared three rectangle measurement selections")
                }
            }
            MeasurementMode.FOUR_POINT_ANGLE -> {
                // 清除三点角度测量的选中状态
                if (isAngleMeasuring) {
                    angleMeasureHelper.clearSelection()
                    Log.d(TAG, "🔄 Cleared three-point angle measurement selections")
                }
                // 清除点测量的选中状态
                if (isPointMeasuring) {
                    pointMeasureHelper.clearSelection()
                    Log.d(TAG, "🔄 Cleared point measurement selections")
                }
                // 清除线段测量的选中状态
                if (isLineMeasuring) {
                    lineMeasureHelper.clearSelection()
                    Log.d(TAG, "🔄 Cleared line measurement selections")
                }
                // 清除水平线测量的选中状态
                if (isHorizonLineMeasuring) {
                    horizonLineMeasureHelper.clearSelection()
                    Log.d(TAG, "🔄 Cleared horizon line measurement selections")
                }
                // 清除垂直线测量的选中状态
                if (isVerticalLineMeasuring) {
                    verticalLineMeasureHelper.clearSelection()
                    Log.d(TAG, "🔄 Cleared vertical line measurement selections")
                }
                //清除平行线
                if (isParallelLinesMeasuring){
                    parallelLinesMeasureHelper.clearSelection()
                    Log.d(TAG, "🔄 Cleared parallel lines measurement selections")
                }
                //三线
                if(isThreeVerticalMeasuring){
                    threeVerticalMeasureHelper.clearSelection()
                    Log.d(TAG, "🔄 Cleared three-vertical measurement selections")
                }
                // 清除矩形测量的选中状态
                if (isRectangleMeasuring) {
                    rectangleMeasureHelper.clearSelection()
                    Log.d(TAG, "🔄 Cleared rectangle measurement selections")
                }
                // 清除三点矩形测量的选中状态
                if (isThreeRectangleMeasuring) {
                    threeRectangleMeasureHelper.clearSelection()
                    Log.d(TAG, "🔄 Cleared three rectangle measurement selections")
                }
            }
            MeasurementMode.POINT -> {
                // 清除三点角度测量的选中状态
                if (isAngleMeasuring) {
                    angleMeasureHelper.clearSelection()
                    Log.d(TAG, "🔄 Cleared three-point angle measurement selections")
                }
                // 清除四点角度测量的选中状态
                if (isFourPointAngleMeasuring) {
                    fourPointAngleHelper.clearSelection()
                    Log.d(TAG, "🔄 Cleared four-point angle measurement selections")
                }
                // 清除线段测量的选中状态
                if (isLineMeasuring) {
                    lineMeasureHelper.clearSelection()
                    Log.d(TAG, "🔄 Cleared line measurement selections")
                }
                // 清除水平线测量的选中状态
                if (isHorizonLineMeasuring) {
                    horizonLineMeasureHelper.clearSelection()
                    Log.d(TAG, "🔄 Cleared horizon line measurement selections")
                }
                // 清除垂直线测量的选中状态
                if (isVerticalLineMeasuring) {
                    verticalLineMeasureHelper.clearSelection()
                    Log.d(TAG, "🔄 Cleared vertical line measurement selections")
                }
                //清除平行线
                if (isParallelLinesMeasuring){
                    parallelLinesMeasureHelper.clearSelection()
                    Log.d(TAG, "🔄 Cleared parallel lines measurement selections")
                }
                //三线
                if(isThreeVerticalMeasuring){
                    threeVerticalMeasureHelper.clearSelection()
                    Log.d(TAG, "🔄 Cleared three-vertical measurement selections")
                }
                // 清除矩形测量的选中状态
                if (isRectangleMeasuring) {
                    rectangleMeasureHelper.clearSelection()
                    Log.d(TAG, "🔄 Cleared rectangle measurement selections")
                }
                // 清除三点矩形测量的选中状态
                if (isThreeRectangleMeasuring) {
                    threeRectangleMeasureHelper.clearSelection()
                    Log.d(TAG, "🔄 Cleared three rectangle measurement selections")
                }
            }
            MeasurementMode.LINE -> {
                // 清除三点角度测量的选中状态
                if (isAngleMeasuring) {
                    angleMeasureHelper.clearSelection()
                    Log.d(TAG, "🔄 Cleared three-point angle measurement selections")
                }
                // 清除四点角度测量的选中状态
                if (isFourPointAngleMeasuring) {
                    fourPointAngleHelper.clearSelection()
                    Log.d(TAG, "🔄 Cleared four-point angle measurement selections")
                }
                // 清除点测量的选中状态
                if (isPointMeasuring) {
                    pointMeasureHelper.clearSelection()
                    Log.d(TAG, "🔄 Cleared point measurement selections")
                }
                // 清除水平线测量的选中状态
                if (isHorizonLineMeasuring) {
                    horizonLineMeasureHelper.clearSelection()
                    Log.d(TAG, "🔄 Cleared horizon line measurement selections")
                }
                // 清除垂直线测量的选中状态
                if (isVerticalLineMeasuring) {
                    verticalLineMeasureHelper.clearSelection()
                    Log.d(TAG, "🔄 Cleared vertical line measurement selections")
                }
                //清除平行线
                if (isParallelLinesMeasuring){
                    parallelLinesMeasureHelper.clearSelection()
                    Log.d(TAG, "🔄 Cleared parallel lines measurement selections")
                }
                //三线
                if(isThreeVerticalMeasuring){
                    threeVerticalMeasureHelper.clearSelection()
                    Log.d(TAG, "🔄 Cleared three-vertical measurement selections")
                }
                // 清除矩形测量的选中状态
                if (isRectangleMeasuring) {
                    rectangleMeasureHelper.clearSelection()
                    Log.d(TAG, "🔄 Cleared rectangle measurement selections")
                }
                // 清除三点矩形测量的选中状态
                if (isThreeRectangleMeasuring) {
                    threeRectangleMeasureHelper.clearSelection()
                    Log.d(TAG, "🔄 Cleared three rectangle measurement selections")
                }
            }
            MeasurementMode.HORIZON_LINE -> {
                // 清除三点角度测量的选中状态
                if (isAngleMeasuring) {
                    angleMeasureHelper.clearSelection()
                    Log.d(TAG, "🔄 Cleared three-point angle measurement selections")
                }
                // 清除四点角度测量的选中状态
                if (isFourPointAngleMeasuring) {
                    fourPointAngleHelper.clearSelection()
                    Log.d(TAG, "🔄 Cleared four-point angle measurement selections")
                }
                // 清除点测量的选中状态
                if (isPointMeasuring) {
                    pointMeasureHelper.clearSelection()
                    Log.d(TAG, "🔄 Cleared point measurement selections")
                }
                // 清除线段测量的选中状态
                if (isLineMeasuring) {
                    lineMeasureHelper.clearSelection()
                    Log.d(TAG, "🔄 Cleared line measurement selections")
                }
                // 清除垂直线测量的选中状态
                if (isVerticalLineMeasuring) {
                    verticalLineMeasureHelper.clearSelection()
                    Log.d(TAG, "🔄 Cleared vertical line measurement selections")
                }
                //清除平行线
                if (isParallelLinesMeasuring){
                    parallelLinesMeasureHelper.clearSelection()
                    Log.d(TAG, "🔄 Cleared parallel lines measurement selections")
                }
                //三线
                if(isThreeVerticalMeasuring){
                    threeVerticalMeasureHelper.clearSelection()
                    Log.d(TAG, "🔄 Cleared three-vertical measurement selections")
                }
                // 清除矩形测量的选中状态
                if (isRectangleMeasuring) {
                    rectangleMeasureHelper.clearSelection()
                    Log.d(TAG, "🔄 Cleared rectangle measurement selections")
                }
                // 清除三点矩形测量的选中状态
                if (isThreeRectangleMeasuring) {
                    threeRectangleMeasureHelper.clearSelection()
                    Log.d(TAG, "🔄 Cleared three rectangle measurement selections")
                }
            }
            MeasurementMode.VERTICAL_LINE -> {
                // 清除三点角度测量的选中状态
                if (isAngleMeasuring) {
                    angleMeasureHelper.clearSelection()
                    Log.d(TAG, "🔄 Cleared three-point angle measurement selections")
                }
                // 清除四点角度测量的选中状态
                if (isFourPointAngleMeasuring) {
                    fourPointAngleHelper.clearSelection()
                    Log.d(TAG, "🔄 Cleared four-point angle measurement selections")
                }
                // 清除点测量的选中状态
                if (isPointMeasuring) {
                    pointMeasureHelper.clearSelection()
                    Log.d(TAG, "🔄 Cleared point measurement selections")
                }
                // 清除线段测量的选中状态
                if (isLineMeasuring) {
                    lineMeasureHelper.clearSelection()
                    Log.d(TAG, "🔄 Cleared line measurement selections")
                }
                // 清除水平线测量的选中状态
                if (isHorizonLineMeasuring) {
                    horizonLineMeasureHelper.clearSelection()
                    Log.d(TAG, "🔄 Cleared horizon line measurement selections")
                }
                //清除平行线
                if (isParallelLinesMeasuring){
                    parallelLinesMeasureHelper.clearSelection()
                    Log.d(TAG, "🔄 Cleared parallel lines measurement selections")
                }
                //三线
                if(isThreeVerticalMeasuring){
                    threeVerticalMeasureHelper.clearSelection()
                    Log.d(TAG, "🔄 Cleared three-vertical measurement selections")
                }
                // 清除矩形测量的选中状态
                if (isRectangleMeasuring) {
                    rectangleMeasureHelper.clearSelection()
                    Log.d(TAG, "🔄 Cleared rectangle measurement selections")
                }
                // 清除三点矩形测量的选中状态
                if (isThreeRectangleMeasuring) {
                    threeRectangleMeasureHelper.clearSelection()
                    Log.d(TAG, "🔄 Cleared three rectangle measurement selections")
                }
            }
            MeasurementMode.PARALLEL_LINES -> {
                // 清除三点角度测量的选中状态
                if (isAngleMeasuring) {
                    angleMeasureHelper.clearSelection()
                    Log.d(TAG, "🔄 Cleared three-point angle measurement selections")
                }
                // 清除四点角度测量的选中状态
                if (isFourPointAngleMeasuring) {
                    fourPointAngleHelper.clearSelection()
                    Log.d(TAG, "🔄 Cleared four-point angle measurement selections")
                }
                // 清除点测量的选中状态
                if (isPointMeasuring) {
                    pointMeasureHelper.clearSelection()
                    Log.d(TAG, "🔄 Cleared point measurement selections")
                }
                // 清除线段测量的选中状态
                if (isLineMeasuring) {
                    lineMeasureHelper.clearSelection()
                    Log.d(TAG, "🔄 Cleared line measurement selections")
                }
                // 清除水平线测量的选中状态
                if (isHorizonLineMeasuring) {
                    horizonLineMeasureHelper.clearSelection()
                    Log.d(TAG, "🔄 Cleared horizon line measurement selections")
                }
                // 清除垂直线测量的选中状态
                if (isVerticalLineMeasuring) {
                    verticalLineMeasureHelper.clearSelection()
                    Log.d(TAG, "🔄 Cleared vertical line measurement selections")
                }
                // 清除三垂足测量的选中状态
                if (isThreeVerticalMeasuring) {
                    threeVerticalMeasureHelper.clearSelection()
                    Log.d(TAG, "🔄 Cleared three vertical measurement selections")
                }
                // 清除矩形测量的选中状态
                if (isRectangleMeasuring) {
                    rectangleMeasureHelper.clearSelection()
                    Log.d(TAG, "🔄 Cleared rectangle measurement selections")
                }
                // 清除三点矩形测量的选中状态
                if (isThreeRectangleMeasuring) {
                    threeRectangleMeasureHelper.clearSelection()
                    Log.d(TAG, "🔄 Cleared three rectangle measurement selections")
                }
            }
            MeasurementMode.THREE_VERTICAL -> {
                // 清除三点角度测量的选中状态
                if (isAngleMeasuring) {
                    angleMeasureHelper.clearSelection()
                    Log.d(TAG, "🔄 Cleared three-point angle measurement selections")
                }
                // 清除四点角度测量的选中状态
                if (isFourPointAngleMeasuring) {
                    fourPointAngleHelper.clearSelection()
                    Log.d(TAG, "🔄 Cleared four-point angle measurement selections")
                }
                // 清除点测量的选中状态
                if (isPointMeasuring) {
                    pointMeasureHelper.clearSelection()
                    Log.d(TAG, "🔄 Cleared point measurement selections")
                }
                // 清除线段测量的选中状态
                if (isLineMeasuring) {
                    lineMeasureHelper.clearSelection()
                    Log.d(TAG, "🔄 Cleared line measurement selections")
                }
                // 清除水平线测量的选中状态
                if (isHorizonLineMeasuring) {
                    horizonLineMeasureHelper.clearSelection()
                    Log.d(TAG, "🔄 Cleared horizon line measurement selections")
                }
                // 清除垂直线测量的选中状态
                if (isVerticalLineMeasuring) {
                    verticalLineMeasureHelper.clearSelection()
                    Log.d(TAG, "🔄 Cleared vertical line measurement selections")
                }
                // 清除平行线测量的选中状态
                if (isParallelLinesMeasuring) {
                    parallelLinesMeasureHelper.clearSelection()
                    Log.d(TAG, "🔄 Cleared parallel lines measurement selections")
                }
                // 清除矩形测量的选中状态
                if (isRectangleMeasuring) {
                    rectangleMeasureHelper.clearSelection()
                    Log.d(TAG, "🔄 Cleared rectangle measurement selections")
                }
                // 清除三点矩形测量的选中状态
                if (isThreeRectangleMeasuring) {
                    threeRectangleMeasureHelper.clearSelection()
                    Log.d(TAG, "🔄 Cleared three rectangle measurement selections")
                }
            }
            MeasurementMode.RECTANGLE -> {
                // 清除三点角度测量的选中状态
                if (isAngleMeasuring) {
                    angleMeasureHelper.clearSelection()
                    Log.d(TAG, "🔄 Cleared three-point angle measurement selections")
                }
                // 清除四点角度测量的选中状态
                if (isFourPointAngleMeasuring) {
                    fourPointAngleHelper.clearSelection()
                    Log.d(TAG, "🔄 Cleared four-point angle measurement selections")
                }
                // 清除点测量的选中状态
                if (isPointMeasuring) {
                    pointMeasureHelper.clearSelection()
                    Log.d(TAG, "🔄 Cleared point measurement selections")
                }
                // 清除线段测量的选中状态
                if (isLineMeasuring) {
                    lineMeasureHelper.clearSelection()
                    Log.d(TAG, "🔄 Cleared line measurement selections")
                }
                // 清除水平线测量的选中状态
                if (isHorizonLineMeasuring) {
                    horizonLineMeasureHelper.clearSelection()
                    Log.d(TAG, "🔄 Cleared horizon line measurement selections")
                }
                // 清除垂直线测量的选中状态
                if (isVerticalLineMeasuring) {
                    verticalLineMeasureHelper.clearSelection()
                    Log.d(TAG, "🔄 Cleared vertical line measurement selections")
                }
                // 清除平行线测量的选中状态
                if (isParallelLinesMeasuring) {
                    parallelLinesMeasureHelper.clearSelection()
                    Log.d(TAG, "🔄 Cleared parallel lines measurement selections")
                }
                // 清除三垂直测量的选中状态
                if (isThreeVerticalMeasuring) {
                    threeVerticalMeasureHelper.clearSelection()
                    Log.d(TAG, "🔄 Cleared three vertical measurement selections")
                }
                // 清除三点矩形测量的选中状态
                if (isThreeRectangleMeasuring) {
                    threeRectangleMeasureHelper.clearSelection()
                    Log.d(TAG, "🔄 Cleared three rectangle measurement selections")
                }
                // 清除椭圆测量的选中状态
                if (isEllipseMeasuring) {
                    ellipseMeasureHelper.clearSelection()
                    Log.d(TAG, "🔄 Cleared ellipse measurement selections")
                }
            }
            MeasurementMode.ELLIPSE -> {
                // 清除三点角度测量的选中状态
                if (isAngleMeasuring) {
                    angleMeasureHelper.clearSelection()
                    Log.d(TAG, "🔄 Cleared three-point angle measurement selections")
                }
                // 清除四点角度测量的选中状态
                if (isFourPointAngleMeasuring) {
                    fourPointAngleHelper.clearSelection()
                    Log.d(TAG, "🔄 Cleared four-point angle measurement selections")
                }
                // 清除点测量的选中状态
                if (isPointMeasuring) {
                    pointMeasureHelper.clearSelection()
                    Log.d(TAG, "🔄 Cleared point measurement selections")
                }
                // 清除线段测量的选中状态
                if (isLineMeasuring) {
                    lineMeasureHelper.clearSelection()
                    Log.d(TAG, "🔄 Cleared line measurement selections")
                }
                // 清除水平线测量的选中状态
                if (isHorizonLineMeasuring) {
                    horizonLineMeasureHelper.clearSelection()
                    Log.d(TAG, "🔄 Cleared horizon line measurement selections")
                }
                // 清除垂直线测量的选中状态
                if (isVerticalLineMeasuring) {
                    verticalLineMeasureHelper.clearSelection()
                    Log.d(TAG, "🔄 Cleared vertical line measurement selections")
                }
                // 清除平行线测量的选中状态
                if (isParallelLinesMeasuring) {
                    parallelLinesMeasureHelper.clearSelection()
                    Log.d(TAG, "🔄 Cleared parallel lines measurement selections")
                }
                // 清除三垂直测量的选中状态
                if (isThreeVerticalMeasuring) {
                    threeVerticalMeasureHelper.clearSelection()
                    Log.d(TAG, "🔄 Cleared three vertical measurement selections")
                }
                // 清除矩形测量的选中状态
                if (isRectangleMeasuring) {
                    rectangleMeasureHelper.clearSelection()
                    Log.d(TAG, "🔄 Cleared rectangle measurement selections")
                }
                // 清除三点矩形测量的选中状态
                if (isThreeRectangleMeasuring) {
                    threeRectangleMeasureHelper.clearSelection()
                    Log.d(TAG, "🔄 Cleared three rectangle measurement selections")
                }
            }
            MeasurementMode.THREE_RECTANGLE -> {
                // 清除三点角度测量的选中状态
                if (isAngleMeasuring) {
                    angleMeasureHelper.clearSelection()
                    Log.d(TAG, "🔄 Cleared three-point angle measurement selections")
                }
                // 清除四点角度测量的选中状态
                if (isFourPointAngleMeasuring) {
                    fourPointAngleHelper.clearSelection()
                    Log.d(TAG, "🔄 Cleared four-point angle measurement selections")
                }
                // 清除点测量的选中状态
                if (isPointMeasuring) {
                    pointMeasureHelper.clearSelection()
                    Log.d(TAG, "🔄 Cleared point measurement selections")
                }
                // 清除线段测量的选中状态
                if (isLineMeasuring) {
                    lineMeasureHelper.clearSelection()
                    Log.d(TAG, "🔄 Cleared line measurement selections")
                }
                // 清除水平线测量的选中状态
                if (isHorizonLineMeasuring) {
                    horizonLineMeasureHelper.clearSelection()
                    Log.d(TAG, "🔄 Cleared horizon line measurement selections")
                }
                // 清除垂直线测量的选中状态
                if (isVerticalLineMeasuring) {
                    verticalLineMeasureHelper.clearSelection()
                    Log.d(TAG, "🔄 Cleared vertical line measurement selections")
                }
                // 清除平行线测量的选中状态
                if (isParallelLinesMeasuring) {
                    parallelLinesMeasureHelper.clearSelection()
                    Log.d(TAG, "🔄 Cleared parallel lines measurement selections")
                }
                // 清除三垂直测量的选中状态
                if (isThreeVerticalMeasuring) {
                    threeVerticalMeasureHelper.clearSelection()
                    Log.d(TAG, "🔄 Cleared three vertical measurement selections")
                }
                // 清除矩形测量的选中状态
                if (isRectangleMeasuring) {
                    rectangleMeasureHelper.clearSelection()
                    Log.d(TAG, "🔄 Cleared rectangle measurement selections")
                }
                // 清除椭圆测量的选中状态
                if (isEllipseMeasuring) {
                    ellipseMeasureHelper.clearSelection()
                    Log.d(TAG, "🔄 Cleared ellipse measurement selections")
                }
            }
        }
    }

    /**
     * 🔍 检查是否正在拖拽测量点 - 支持所有测量类型
     */
    fun isDraggingPoint(): Boolean {
        return if (isInitialized) {
            when {
                isAngleMeasuring -> angleMeasureHelper.isDraggingPoint()
                isFourPointAngleMeasuring -> fourPointAngleHelper.isDraggingPoint()
                isPointMeasuring -> pointMeasureHelper.isDraggingPoint()
                isLineMeasuring -> lineMeasureHelper.isDraggingPoint()
                isHorizonLineMeasuring -> horizonLineMeasureHelper.isDraggingPoint()
                isVerticalLineMeasuring -> verticalLineMeasureHelper.isDraggingPoint()
                // 平行线测量助手没有isDraggingPoint方法，暂时返回false
                isParallelLinesMeasuring -> false
                isThreeVerticalMeasuring -> threeVerticalMeasureHelper.isDraggingPoint()
                isRectangleMeasuring -> rectangleMeasureHelper.isDraggingPoint()
                isThreeRectangleMeasuring -> threeRectangleMeasureHelper.isDraggingPoint()
                isEllipseMeasuring -> ellipseMeasureHelper.isDraggingPoint()
                else -> false
            }
        } else {
            false
        }
    }

    /**
     * 🎯 添加新的角度测量 - 在测量模式下生成新角度
     */
    fun addNewAngleMeasurement(): Boolean {
        if (!isInitialized) {
            Log.e(TAG, "❌ MeasurementManager not initialized")
            return false
        }

        if (!isAngleMeasuring) {
            Log.e(TAG, "❌ Not in angle measurement mode")
            return false
        }

        try {
            Log.d(TAG, "🎯 Adding new angle measurement...")

            // 🎯 直接添加新的角度测量
            val measurementId = angleMeasureHelper.addNewMeasurement()

            if (measurementId.isNotEmpty()) {
                // 🔄 立即更新混合覆盖层显示
                updateMixedOverlayDisplay()
                forceOverlayDisplay()

                // 延迟再次强制显示确保时序
                overlayView.post {
                    forceOverlayDisplay()
                }

                Log.d(TAG, "✅ New angle measurement added successfully: $measurementId")
                return true
            } else {
                Log.e(TAG, "❌ Failed to add new angle measurement")
                return false
            }

        } catch (e: Exception) {
            Log.e(TAG, "❌ Failed to add new angle measurement: ${e.message}")
            e.printStackTrace()
            return false
        }
    }

    /**
     * 🎯 添加新的点测量 - 在测量模式下生成新点
     */
    fun addNewPointMeasurement(): Boolean {
        if (!isInitialized) {
            Log.e(TAG, "❌ MeasurementManager not initialized")
            return false
        }

        if (!isPointMeasuring) {
            Log.e(TAG, "❌ Not in point measurement mode")
            return false
        }

        try {
            Log.d(TAG, "🎯 Adding new point measurement...")

            // 🎯 直接添加新的点测量
            val measurementId = pointMeasureHelper.addNewMeasurement()

            if (measurementId.isNotEmpty()) {
                // 🔄 立即更新混合覆盖层显示
                updateMixedOverlayDisplay()
                forceOverlayDisplay()

                // 延迟再次强制显示确保时序
                overlayView.post {
                    forceOverlayDisplay()
                }

                Log.d(TAG, "✅ New point measurement added successfully: $measurementId")
                return true
            } else {
                Log.e(TAG, "❌ Failed to add new point measurement")
                return false
            }

        } catch (e: Exception) {
            Log.e(TAG, "❌ Failed to add new point measurement: ${e.message}")
            e.printStackTrace()
            return false
        }
    }

    /**
     * 🎯 添加新的四点角度测量 - 在测量模式下生成新四点角度
     */
    fun addNewFourPointAngleMeasurement(): Boolean {
        if (!isInitialized) {
            Log.e(TAG, "❌ MeasurementManager not initialized")
            return false
        }

        if (!isFourPointAngleMeasuring) {
            Log.e(TAG, "❌ Not in four-point angle measurement mode")
            return false
        }

        try {
            Log.d(TAG, "🎯 Adding new four-point angle measurement...")

            // 🎯 直接添加新的四点角度测量
            val measurementId = fourPointAngleHelper.addNewMeasurement()

            if (measurementId.isNotEmpty()) {
                // 🔄 立即更新混合覆盖层显示
                updateMixedOverlayDisplay()
                forceOverlayDisplay()

                // 延迟再次强制显示确保时序
                overlayView.post {
                    forceOverlayDisplay()
                }

                Log.d(TAG, "✅ New four-point angle measurement added successfully: $measurementId")
                return true
            } else {
                Log.e(TAG, "❌ Failed to add new four-point angle measurement")
                return false
            }

        } catch (e: Exception) {
            Log.e(TAG, "❌ Failed to add new four-point angle measurement: ${e.message}")
            e.printStackTrace()
            return false
        }
    }

    /**
     * 📏 添加新的线段测量 - 在测量模式下生成新线段
     */
    fun addNewLineMeasurement(): Boolean {
        if (!isInitialized) {
            Log.e(TAG, "❌ MeasurementManager not initialized")
            return false
        }

        if (!isLineMeasuring) {
            Log.e(TAG, "❌ Not in line measurement mode")
            return false
        }

        try {
            Log.d(TAG, "📏 Adding new line measurement...")

            // 📏 直接添加新的线段测量
            val measurementId = lineMeasureHelper.addNewMeasurement()

            if (measurementId.isNotEmpty()) {
                // 🔄 立即更新混合覆盖层显示
                updateMixedOverlayDisplay()
                forceOverlayDisplay()

                // 延迟再次强制显示确保时序
                overlayView.post {
                    forceOverlayDisplay()
                }

                Log.d(TAG, "✅ New line measurement added successfully: $measurementId")
                return true
            } else {
                Log.e(TAG, "❌ Failed to add new line measurement")
                return false
            }

        } catch (e: Exception) {
            Log.e(TAG, "❌ Failed to add new line measurement: ${e.message}")
            e.printStackTrace()
            return false
        }
    }

    /**
     * 📏 添加新的水平线测量 - 在测量模式下生成新水平线
     */
    fun addNewHorizonLineMeasurement(): Boolean {
        if (!isInitialized) {
            Log.e(TAG, "❌ MeasurementManager not initialized")
            return false
        }

        if (!isHorizonLineMeasuring) {
            Log.e(TAG, "❌ Not in horizon line measurement mode")
            return false
        }

        try {
            Log.d(TAG, "📏 Adding new horizon line measurement...")

            // 📏 直接添加新的水平线测量
            val measurementId = horizonLineMeasureHelper.addNewMeasurement()

            if (measurementId.isNotEmpty()) {
                // 🔄 立即更新混合覆盖层显示
                updateMixedOverlayDisplay()
                forceOverlayDisplay()

                // 延迟再次强制显示确保时序
                overlayView.post {
                    forceOverlayDisplay()
                }

                Log.d(TAG, "✅ New horizon line measurement added successfully: $measurementId")
                return true
            } else {
                Log.e(TAG, "❌ Failed to add new horizon line measurement")
                return false
            }

        } catch (e: Exception) {
            Log.e(TAG, "❌ Failed to add new horizon line measurement: ${e.message}")
            e.printStackTrace()
            return false
        }
    }

    /**
     * 📏 添加新的垂直线测量 - 在测量模式下生成新垂直线
     */
    fun addNewVerticalLineMeasurement(): Boolean {
        if (!isInitialized) {
            Log.e(TAG, "❌ MeasurementManager not initialized")
            return false
        }

        if (!isVerticalLineMeasuring) {
            Log.e(TAG, "❌ Not in vertical line measurement mode")
            return false
        }

        try {
            Log.d(TAG, "📏 Adding new vertical line measurement...")

            // 📏 直接添加新的垂直线测量
            val measurementId = verticalLineMeasureHelper.addNewMeasurement()

            if (measurementId.isNotEmpty()) {
                // 🔄 立即更新混合覆盖层显示
                updateMixedOverlayDisplay()
                forceOverlayDisplay()

                // 延迟再次强制显示确保时序
                overlayView.post {
                    forceOverlayDisplay()
                }

                Log.d(TAG, "✅ New vertical line measurement added successfully: $measurementId")
                return true
            } else {
                Log.e(TAG, "❌ Failed to add new vertical line measurement")
                return false
            }

        } catch (e: Exception) {
            Log.e(TAG, "❌ Failed to add new vertical line measurement: ${e.message}")
            e.printStackTrace()
            return false
        }
    }

    /**
     * 📏 添加新的平行线测量 - 在测量模式下生成新平行线
     */
    fun addNewParallelLinesMeasurement(): Boolean {
        if (!isInitialized) {
            Log.e(TAG, "❌ MeasurementManager not initialized")
            return false
        }

        if (!isParallelLinesMeasuring) {
            Log.e(TAG, "❌ Not in parallel lines measurement mode")
            return false
        }

        try {
            Log.d(TAG, "📏 Adding new parallel lines measurement...")

            // 📏 直接添加新的平行线测量
            val measurementId = parallelLinesMeasureHelper.addNewMeasurement()

            if (measurementId.isNotEmpty()) {
                // 🔄 立即更新混合覆盖层显示
                updateMixedOverlayDisplay()
                forceOverlayDisplay()

                // 延迟再次强制显示确保时序
                overlayView.post {
                    forceOverlayDisplay()
                }

                Log.d(TAG, "✅ New parallel lines measurement added successfully: $measurementId")
                return true
            } else {
                Log.e(TAG, "❌ Failed to add new parallel lines measurement")
                return false
            }

        } catch (e: Exception) {
            Log.e(TAG, "❌ Failed to add new parallel lines measurement: ${e.message}")
            e.printStackTrace()
            return false
        }
    }

    /**
     * 📏 添加新的三垂直测量 - 在测量模式下生成新三垂直测量
     */
    fun addNewThreeVerticalMeasurement(): Boolean {
        if (!isInitialized) {
            Log.e(TAG, "❌ MeasurementManager not initialized")
            return false
        }

        if (!isThreeVerticalMeasuring) {
            Log.e(TAG, "❌ Not in three vertical measurement mode")
            return false
        }

        try {
            Log.d(TAG, "📏 Adding new three vertical measurement...")

            // 📏 直接添加新的三垂直测量
            val measurementId = threeVerticalMeasureHelper.addNewMeasurement()

            if (measurementId.isNotEmpty()) {
                // 🔄 立即更新混合覆盖层显示
                updateMixedOverlayDisplay()
                forceOverlayDisplay()

                // 延迟再次强制显示确保时序
                overlayView.post {
                    forceOverlayDisplay()
                }

                Log.d(TAG, "✅ New three vertical measurement added successfully: $measurementId")
                return true
            } else {
                Log.e(TAG, "❌ Failed to add new three vertical measurement")
                return false
            }

        } catch (e: Exception) {
            Log.e(TAG, "❌ Failed to add new three vertical measurement: ${e.message}")
            e.printStackTrace()
            return false
        }
    }

    /**
     * 🗑️ 删除选中的测量 - 支持所有测量类型，带状态保护机制
     */
    fun deleteSelectedMeasurement(): Boolean {
        if (!isInitialized) {
            Log.e(TAG, "❌ MeasurementManager not initialized")
            return false
        }

        // 🔍 验证状态一致性
        Log.d(TAG, "🔍 Current state before validation: activeMeasurementMode=$activeMeasurementMode")
        if (!validateState()) {
            Log.e(TAG, "❌ Invalid state detected, cannot delete measurement")
            return false
        }

        if (!isMixedMeasuring()) {
            Log.e(TAG, "❌ Not in any measurement mode")
            return false
        }

        // 🔧 删除操作保护：保存当前选中状态
        val savedSelectionState = saveCurrentSelectionState()
        Log.d(TAG, "🔒 Saved selection state before deletion: $savedSelectionState")

        try {
            Log.d(TAG, "🗑️ Deleting selected measurement...")

            // 🎯 智能删除：优先删除当前激活模式的选中测量
            var deleted = false

            // 第一优先级：当前激活模式的选中测量
            Log.d(TAG, "🔍 Checking first priority - activeMeasurementMode: $activeMeasurementMode")
            when (activeMeasurementMode) {
                MeasurementMode.ANGLE -> {
                    if (isAngleMeasuring && angleMeasureHelper.hasSelectedMeasurement()) {
                        Log.d(TAG, "🎯 Deleting selected measurement from current active mode: ANGLE")
                        deleted = angleMeasureHelper.deleteSelectedMeasurement()
                        if (deleted) {
                            updateMixedOverlayDisplay()
                            forceOverlayDisplay()
                            Log.d(TAG, "✅ Angle measurement deleted from active mode")
                        }
                    }
                }
                MeasurementMode.FOUR_POINT_ANGLE -> {
                    if (isFourPointAngleMeasuring && fourPointAngleHelper.hasSelectedMeasurement()) {
                        Log.d(TAG, "🎯 Deleting selected measurement from current active mode: FOUR_POINT_ANGLE")
                        deleted = fourPointAngleHelper.deleteSelectedMeasurement()
                        if (deleted) {
                            updateMixedOverlayDisplay()
                            forceOverlayDisplay()
                            Log.d(TAG, "✅ Four-point angle measurement deleted from active mode")
                        }
                    }
                }
                MeasurementMode.POINT -> {
                    if (isPointMeasuring && pointMeasureHelper.hasSelectedMeasurement()) {
                        Log.d(TAG, "🎯 Deleting selected measurement from current active mode: POINT")
                        deleted = pointMeasureHelper.deleteSelectedMeasurement()
                        if (deleted) {
                            updateMixedOverlayDisplay()
                            forceOverlayDisplay()
                            Log.d(TAG, "✅ Point measurement deleted from active mode")
                        }
                    }
                }
                MeasurementMode.LINE -> {
                    if (isLineMeasuring && lineMeasureHelper.hasSelectedMeasurement()) {
                        Log.d(TAG, "🎯 Deleting selected measurement from current active mode: LINE")
                        deleted = lineMeasureHelper.deleteSelectedMeasurement()
                        if (deleted) {
                            updateMixedOverlayDisplay()
                            forceOverlayDisplay()
                            Log.d(TAG, "✅ Line measurement deleted from active mode")
                        }
                    }
                }
                MeasurementMode.HORIZON_LINE -> {
                    if (isHorizonLineMeasuring && horizonLineMeasureHelper.hasSelectedMeasurement()) {
                        Log.d(TAG, "🎯 Deleting selected measurement from current active mode: HORIZON_LINE")
                        deleted = horizonLineMeasureHelper.deleteSelectedMeasurement()
                        if (deleted) {
                            updateMixedOverlayDisplay()
                            forceOverlayDisplay()
                            Log.d(TAG, "✅ Horizon line measurement deleted from active mode")
                        }
                    }
                }
                MeasurementMode.VERTICAL_LINE -> {
                    if (isVerticalLineMeasuring && verticalLineMeasureHelper.hasSelectedMeasurement()) {
                        Log.d(TAG, "🎯 Deleting selected measurement from current active mode: VERTICAL_LINE")
                        deleted = verticalLineMeasureHelper.deleteSelectedMeasurement()
                        if (deleted) {
                            updateMixedOverlayDisplay()
                            forceOverlayDisplay()
                            Log.d(TAG, "✅ Vertical line measurement deleted from active mode")
                        }
                    }
                }
                MeasurementMode.PARALLEL_LINES -> {
                    if (isParallelLinesMeasuring && parallelLinesMeasureHelper.hasSelectedMeasurement()){
                        Log.d(TAG, "🎯 Deleting selected measurement from current active mode: PARALLEL_LINES")
                        deleted = parallelLinesMeasureHelper.deleteSelectedMeasurement()
                        if (deleted) {
                            updateMixedOverlayDisplay()
                            forceOverlayDisplay()
                            Log.d(TAG, "✅ Parallel lines measurement deleted from active mode")
                        }
                    }
                }
                MeasurementMode.THREE_VERTICAL -> {
                    //Log.d(TAG, "🎯 aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa: $isThreeVerticalMeasuring")
                    if (isThreeVerticalMeasuring && threeVerticalMeasureHelper.hasSelectedMeasurement()){
                        Log.d(TAG, "🎯 Deleting selected measurement from current active mode: THREE_VERTICAL")
                        deleted = threeVerticalMeasureHelper.deleteSelectedMeasurement()
                        if (deleted) {
                            updateMixedOverlayDisplay()
                            forceOverlayDisplay()
                            Log.d(TAG, "✅ Three vertical measurement deleted from active mode")
                        }
                    }
                }
                MeasurementMode.RECTANGLE -> {
                    if (isRectangleMeasuring && rectangleMeasureHelper.hasSelectedMeasurement()) {
                        Log.d(TAG, "🎯 Deleting selected measurement from current active mode: RECTANGLE")
                        deleted = rectangleMeasureHelper.deleteSelectedMeasurement()
                        if (deleted) {
                            updateMixedOverlayDisplay()
                            forceOverlayDisplay()
                            Log.d(TAG, "✅ Rectangle measurement deleted from active mode")
                        }
                    }
                }
                MeasurementMode.ELLIPSE -> {
                    if (isEllipseMeasuring && ellipseMeasureHelper.hasSelectedMeasurement()) {
                        Log.d(TAG, "🎯 Deleting selected measurement from current active mode: ELLIPSE")
                        deleted = ellipseMeasureHelper.deleteSelectedMeasurement()
                        if (deleted) {
                            updateMixedOverlayDisplay()
                            forceOverlayDisplay()
                            Log.d(TAG, "✅ Ellipse measurement deleted from active mode")
                        }
                    }
                }
                MeasurementMode.THREE_RECTANGLE -> {
                    if (isThreeRectangleMeasuring && threeRectangleMeasureHelper.hasSelectedMeasurement()) {
                        Log.d(TAG, "🎯 Deleting selected measurement from current active mode: THREE_RECTANGLE")
                        deleted = threeRectangleMeasureHelper.deleteSelectedMeasurement()
                        if (deleted) {
                            updateMixedOverlayDisplay()
                            forceOverlayDisplay()
                            Log.d(TAG, "✅ Three rectangle measurement deleted from active mode")
                        }
                    }
                }
                null -> {
                    Log.d(TAG, "🔍 No active measurement mode, will check all modes for selected measurements")
                }
            }
            // 第二优先级：检查其他模式是否有选中的测量
            if (!deleted) {
                Log.d(TAG, "🔍 Current active mode has no selected measurement, checking other modes...")

                if (isAngleMeasuring && angleMeasureHelper.hasSelectedMeasurement()) {
                    Log.d(TAG, "🎯 Found selected measurement in ANGLE mode")
                    deleted = angleMeasureHelper.deleteSelectedMeasurement()
                    if (deleted) {
                        activeMeasurementMode = MeasurementMode.ANGLE
                        updateMixedOverlayDisplay()
                        forceOverlayDisplay()
                        Log.d(TAG, "✅ Angle measurement deleted from secondary check")
                    }
                } else if (isFourPointAngleMeasuring && fourPointAngleHelper.hasSelectedMeasurement()) {
                    Log.d(TAG, "🎯 Found selected measurement in FOUR_POINT_ANGLE mode")
                    deleted = fourPointAngleHelper.deleteSelectedMeasurement()
                    if (deleted) {
                        activeMeasurementMode = MeasurementMode.FOUR_POINT_ANGLE
                        updateMixedOverlayDisplay()
                        forceOverlayDisplay()
                        Log.d(TAG, "✅ Four-point angle measurement deleted from secondary check")
                    }
                } else if (isPointMeasuring && pointMeasureHelper.hasSelectedMeasurement()) {
                    Log.d(TAG, "🎯 Found selected measurement in POINT mode")
                    deleted = pointMeasureHelper.deleteSelectedMeasurement()
                    if (deleted) {
                        activeMeasurementMode = MeasurementMode.POINT
                        updateMixedOverlayDisplay()
                        forceOverlayDisplay()
                        Log.d(TAG, "✅ Point measurement deleted from secondary check")
                    }
                } else if (isLineMeasuring && lineMeasureHelper.hasSelectedMeasurement()) {
                    Log.d(TAG, "🎯 Found selected measurement in LINE mode")
                    deleted = lineMeasureHelper.deleteSelectedMeasurement()
                    if (deleted) {
                        activeMeasurementMode = MeasurementMode.LINE
                        updateMixedOverlayDisplay()
                        forceOverlayDisplay()
                        Log.d(TAG, "✅ Line measurement deleted from secondary check")
                    }
                } else if (isHorizonLineMeasuring && horizonLineMeasureHelper.hasSelectedMeasurement()) {
                    Log.d(TAG, "🎯 Found selected measurement in HORIZON_LINE mode")
                    deleted = horizonLineMeasureHelper.deleteSelectedMeasurement()
                    if (deleted) {
                        activeMeasurementMode = MeasurementMode.HORIZON_LINE
                        updateMixedOverlayDisplay()
                        forceOverlayDisplay()
                        Log.d(TAG, "✅ Horizon line measurement deleted from secondary check")
                    }
                } else if (isVerticalLineMeasuring && verticalLineMeasureHelper.hasSelectedMeasurement()) {
                    Log.d(TAG, "🎯 Found selected measurement in VERTICAL_LINE mode")
                    deleted = verticalLineMeasureHelper.deleteSelectedMeasurement()
                    if (deleted) {
                        activeMeasurementMode = MeasurementMode.VERTICAL_LINE
                        updateMixedOverlayDisplay()
                        forceOverlayDisplay()
                        Log.d(TAG, "✅ Vertical line measurement deleted from secondary check")
                    }
                } else if (isParallelLinesMeasuring && parallelLinesMeasureHelper.hasSelectedMeasurement()) {
                    Log.d(TAG, "🎯 Found selected measurement in PARALLEL_LINES mode")
                    deleted = parallelLinesMeasureHelper.deleteSelectedMeasurement()
                    if (deleted) {
                        activeMeasurementMode = MeasurementMode.PARALLEL_LINES
                        updateMixedOverlayDisplay()
                        forceOverlayDisplay()
                        Log.d(TAG, "✅ Parallel lines measurement deleted from secondary check")
                    }
                } else if (isThreeVerticalMeasuring && threeVerticalMeasureHelper.hasSelectedMeasurement()) {
                    Log.d(TAG, "🎯 Found selected measurement in THREE_VERTICAL mode")
                    deleted = threeVerticalMeasureHelper.deleteSelectedMeasurement()
                    if (deleted) {
                        activeMeasurementMode = MeasurementMode.THREE_VERTICAL
                        updateMixedOverlayDisplay()
                        forceOverlayDisplay()
                        Log.d(TAG, "✅ Three vertical measurement deleted from secondary check")
                    }
                } else if (isRectangleMeasuring && rectangleMeasureHelper.hasSelectedMeasurement()) {
                    Log.d(TAG, "🎯 Found selected measurement in RECTANGLE mode")
                    deleted = rectangleMeasureHelper.deleteSelectedMeasurement()
                    if (deleted) {
                        activeMeasurementMode = MeasurementMode.RECTANGLE
                        updateMixedOverlayDisplay()
                        forceOverlayDisplay()
                        Log.d(TAG, "✅ Rectangle measurement deleted from secondary check")
                    }
                } else if (isEllipseMeasuring && ellipseMeasureHelper.hasSelectedMeasurement()) {
                    Log.d(TAG, "🎯 Found selected measurement in ELLIPSE mode")
                    deleted = ellipseMeasureHelper.deleteSelectedMeasurement()
                    if (deleted) {
                        activeMeasurementMode = MeasurementMode.ELLIPSE
                        updateMixedOverlayDisplay()
                        forceOverlayDisplay()
                        Log.d(TAG, "✅ Ellipse measurement deleted from secondary check")
                    }
                } else if (isThreeRectangleMeasuring && threeRectangleMeasureHelper.hasSelectedMeasurement()) {
                    Log.d(TAG, "🎯 Found selected measurement in THREE_RECTANGLE mode")
                    deleted = threeRectangleMeasureHelper.deleteSelectedMeasurement()
                    if (deleted) {
                        activeMeasurementMode = MeasurementMode.THREE_RECTANGLE
                        updateMixedOverlayDisplay()
                        forceOverlayDisplay()
                        Log.d(TAG, "✅ Three rectangle measurement deleted from secondary check")
                    }
                }
            }
            // 第三优先级：自动选中删除（后备机制）
            if (!deleted) {
                Log.d(TAG, "🔍 No selected measurements found, trying auto-select deletion as fallback...")

                if (isAngleMeasuring && angleMeasureHelper.getMeasurementCount() > 0) {
                    Log.d(TAG, "🔄 Auto-deleting from angle measurements")
                    deleted = angleMeasureHelper.deleteSelectedMeasurement()
                    if (deleted) {
                        activeMeasurementMode = MeasurementMode.ANGLE
                        updateMixedOverlayDisplay()
                        forceOverlayDisplay()
                        Log.d(TAG, "✅ Angle measurement auto-deleted")
                    }
                } else if (isFourPointAngleMeasuring && fourPointAngleHelper.getMeasurementCount() > 0) {
                    Log.d(TAG, "🔄 Auto-deleting from four-point angle measurements")
                    deleted = fourPointAngleHelper.deleteSelectedMeasurement()
                    if (deleted) {
                        activeMeasurementMode = MeasurementMode.FOUR_POINT_ANGLE
                        updateMixedOverlayDisplay()
                        forceOverlayDisplay()
                        Log.d(TAG, "✅ Four-point angle measurement auto-deleted")
                    }
                } else if (isPointMeasuring && pointMeasureHelper.getMeasurementCount() > 0) {
                    Log.d(TAG, "🔄 Auto-deleting from point measurements")
                    deleted = pointMeasureHelper.deleteSelectedMeasurement()
                    if (deleted) {
                        activeMeasurementMode = MeasurementMode.POINT
                        updateMixedOverlayDisplay()
                        forceOverlayDisplay()
                        Log.d(TAG, "✅ Point measurement auto-deleted")
                    }
                } else if (isLineMeasuring && lineMeasureHelper.getMeasurementCount() > 0) {
                    Log.d(TAG, "🔄 Auto-deleting from line measurements")
                    deleted = lineMeasureHelper.deleteSelectedMeasurement()
                    if (deleted) {
                        activeMeasurementMode = MeasurementMode.LINE
                        updateMixedOverlayDisplay()
                        forceOverlayDisplay()
                        Log.d(TAG, "✅ Line measurement auto-deleted")
                    }
                } else if (isHorizonLineMeasuring && horizonLineMeasureHelper.getMeasurementCount() > 0) {
                    Log.d(TAG, "🔄 Auto-deleting from horizon line measurements")
                    deleted = horizonLineMeasureHelper.deleteSelectedMeasurement()
                    if (deleted) {
                        activeMeasurementMode = MeasurementMode.HORIZON_LINE
                        updateMixedOverlayDisplay()
                        forceOverlayDisplay()
                        Log.d(TAG, "✅ Horizon line measurement auto-deleted")
                    }
                } else if (isVerticalLineMeasuring && verticalLineMeasureHelper.getMeasurementCount() > 0) {
                    Log.d(TAG, "🔄 Auto-deleting from vertical line measurements")
                    deleted = verticalLineMeasureHelper.deleteSelectedMeasurement()
                    if (deleted) {
                        activeMeasurementMode = MeasurementMode.VERTICAL_LINE
                        updateMixedOverlayDisplay()
                        forceOverlayDisplay()
                        Log.d(TAG, "✅ Vertical line measurement auto-deleted")
                    }
                } else if (isParallelLinesMeasuring && parallelLinesMeasureHelper.getMeasurementCount() > 0) {
                    Log.d(TAG, "🔄 Auto-deleting from parallel lines measurements")
                    deleted = parallelLinesMeasureHelper.deleteSelectedMeasurement()
                    if (deleted) {
                        activeMeasurementMode = MeasurementMode.PARALLEL_LINES
                        updateMixedOverlayDisplay()
                        forceOverlayDisplay()
                        Log.d(TAG, "✅ Parallel lines measurement auto-deleted")
                    }
                } else if (isThreeVerticalMeasuring && threeVerticalMeasureHelper.getMeasurementCount() > 0) {
                    Log.d(TAG, "🔄 Auto-deleting from three vertical measurements")
                    deleted = threeVerticalMeasureHelper.deleteSelectedMeasurement()
                    if (deleted) {
                        activeMeasurementMode = MeasurementMode.THREE_VERTICAL
                        updateMixedOverlayDisplay()
                        forceOverlayDisplay()
                        Log.d(TAG, "✅ Three vertical measurement auto-deleted")
                    }
                } else if (isRectangleMeasuring && rectangleMeasureHelper.getMeasurementCount() > 0) {
                    Log.d(TAG, "🔄 Auto-deleting from rectangle measurements")
                    deleted = rectangleMeasureHelper.deleteSelectedMeasurement()
                    if (deleted) {
                        activeMeasurementMode = MeasurementMode.RECTANGLE
                        updateMixedOverlayDisplay()
                        forceOverlayDisplay()
                        Log.d(TAG, "✅ Rectangle measurement auto-deleted")
                    }
                } else if (isEllipseMeasuring && ellipseMeasureHelper.getMeasurementCount() > 0) {
                    Log.d(TAG, "🔄 Auto-deleting from ellipse measurements")
                    deleted = ellipseMeasureHelper.deleteSelectedMeasurement()
                    if (deleted) {
                        activeMeasurementMode = MeasurementMode.ELLIPSE
                        updateMixedOverlayDisplay()
                        forceOverlayDisplay()
                        Log.d(TAG, "✅ Ellipse measurement auto-deleted")
                    }
                } else if (isThreeRectangleMeasuring && threeRectangleMeasureHelper.getMeasurementCount() > 0) {
                    Log.d(TAG, "🔄 Auto-deleting from three rectangle measurements")
                    deleted = threeRectangleMeasureHelper.deleteSelectedMeasurement()
                    if (deleted) {
                        activeMeasurementMode = MeasurementMode.THREE_RECTANGLE
                        updateMixedOverlayDisplay()
                        forceOverlayDisplay()
                        Log.d(TAG, "✅ Three rectangle measurement auto-deleted")
                    }
                }
            }

            if (!deleted) {
                Log.w(TAG, "⚠️ No selected measurement to delete")
            }

            return deleted

        } catch (e: Exception) {
            Log.e(TAG, "❌ Failed to delete selected measurement: ${e.message}")
            e.printStackTrace()
            return false
        }
    }
    
    /**
     * 🧹 清理资源
     */
    fun cleanup() {
        try {
            Log.d(TAG, "🧹 Cleaning up MeasurementManager")
            
            stopAngleMeasurement()
            angleMeasureHelper.clearAllMeasurements()
            overlayView.clearMeasurement()
            
            isInitialized = false
            currentBitmap = null
            
            Log.d(TAG, "✅ MeasurementManager cleaned up")
            
        } catch (e: Exception) {
            Log.e(TAG, "❌ Failed to cleanup MeasurementManager: ${e.message}")
        }
    }
}
