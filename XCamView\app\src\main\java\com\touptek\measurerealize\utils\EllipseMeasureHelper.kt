package com.touptek.measurerealize.utils

import android.graphics.*
import android.util.Log
import android.view.MotionEvent
import com.touptek.measurerealize.TpImageView
import com.touptek.measurerealize.utils.RectangleMeasureHelper.Companion
import java.util.*
import kotlin.math.*

/**
 * 🔵 椭圆测量实例 - 专业级数据结构
 */
data class EllipseMeasurement(
    val id: String = UUID.randomUUID().toString(),
    var viewPoints: MutableList<PointF> = mutableListOf(),  // [center, majorAxisEnd, minorAxisEnd] 视图坐标
    var bitmapPoints: MutableList<PointF> = mutableListOf(), // [center, majorAxisEnd, minorAxisEnd] 位图坐标
    var isSelected: Boolean = false,
    var isEditing: Boolean = false,
    var isCompleted: Boolean = false,
    var textPosition: PointF? = null,    // 数值文本位置
    var creationTime: Long = System.currentTimeMillis(),
    var lastModified: Long = System.currentTimeMillis(),
    
    // 缓存计算结果
    private var cachedArea: Double? = null,
    private var cachedPerimeter: Double? = null,
    private var cacheValidTime: Long = 0L
) {
    /**
     * 🔵 计算椭圆面积（使用位图坐标 - 真实面积，不受缩放影响）
     */
    fun calculateArea(): Double {
        val currentTime = System.currentTimeMillis()
        
        // 检查缓存是否有效（100ms内）
        if (cachedArea != null && currentTime - cacheValidTime < 100L) {
            return cachedArea!!
        }
        
        // 重新计算并缓存
        cachedArea = computeArea()
        cacheValidTime = currentTime
        return cachedArea!!
    }
    
    private fun computeArea(): Double {
        if (bitmapPoints.size < 3) return 0.0
        
        val center = bitmapPoints[0]
        val majorEnd = bitmapPoints[1]
        val minorEnd = bitmapPoints[2]
        
        val semiMajorAxis = sqrt((majorEnd.x - center.x).pow(2) + (majorEnd.y - center.y).pow(2))
        val semiMinorAxis = sqrt((minorEnd.x - center.x).pow(2) + (minorEnd.y - center.y).pow(2))
        
        return PI * semiMajorAxis * semiMinorAxis
    }
    
    /**
     * 🔵 计算椭圆周长（使用位图坐标 - 真实周长，不受缩放影响）
     * 使用Ramanujan近似公式，精度高达99.5%
     */
    fun calculatePerimeter(): Double {
        val currentTime = System.currentTimeMillis()
        
        // 检查缓存是否有效（100ms内）
        if (cachedPerimeter != null && currentTime - cacheValidTime < 100L) {
            return cachedPerimeter!!
        }
        
        // 重新计算并缓存
        cachedPerimeter = computePerimeter()
        cacheValidTime = currentTime
        return cachedPerimeter!!
    }
    
    private fun computePerimeter(): Double {
        if (bitmapPoints.size < 3) return 0.0
        
        val center = bitmapPoints[0]
        val majorEnd = bitmapPoints[1]
        val minorEnd = bitmapPoints[2]
        
        val a = sqrt((majorEnd.x - center.x).pow(2) + (majorEnd.y - center.y).pow(2))
        val b = sqrt((minorEnd.x - center.x).pow(2) + (minorEnd.y - center.y).pow(2))
        
        // Ramanujan近似公式 - 精度高达99.5%
        val h = ((a - b) / (a + b)).pow(2)
        return PI * (a + b) * (1 + (3 * h) / (10 + sqrt(4 - 3 * h)))
    }
    
    /**
     * 🔵 更新长轴端点（自由移动）
     */
    fun updateMajorAxisEnd(newPoint: PointF) {
        if (viewPoints.size < 3) return
        
        // 更新长轴端点
        viewPoints[1] = newPoint
        
        // 重新计算椭圆中心（长轴中点）
        val majorStart = PointF(2 * viewPoints[0].x - newPoint.x, 2 * viewPoints[0].y - newPoint.y)
        viewPoints[0] = PointF(
            (majorStart.x + newPoint.x) / 2f,
            (majorStart.y + newPoint.y) / 2f
        )
        
        // 约束短轴端点到新的垂直方向
        constrainMinorAxisToPerpendicularDirection()
        
        invalidateCache()
        lastModified = System.currentTimeMillis()
    }
    
    /**
     * 🔵 更新短轴端点（垂直约束）
     */
    fun updateMinorAxisEnd(newPoint: PointF) {
        if (viewPoints.size < 3) return
        
        val constrainedPoint = projectToPerpendicularLine(newPoint)
        viewPoints[2] = constrainedPoint
        
        invalidateCache()
        lastModified = System.currentTimeMillis()
    }
    
    /**
     * 🔵 将点投影到长轴的垂直线上
     */
    private fun projectToPerpendicularLine(point: PointF): PointF {
        if (viewPoints.size < 2) return point
        
        val center = viewPoints[0]
        val majorEnd = viewPoints[1]
        
        // 长轴向量
        val axisVector = PointF(majorEnd.x - center.x, majorEnd.y - center.y)
        
        // 垂直向量（旋转90度）
        val perpVector = PointF(-axisVector.y, axisVector.x)
        
        // 归一化垂直向量
        val perpLength = sqrt(perpVector.x.pow(2) + perpVector.y.pow(2))
        if (perpLength == 0f) return center
        
        val normalizedPerp = PointF(perpVector.x / perpLength, perpVector.y / perpLength)
        
        // 从中心到拖拽点的向量
        val toCursor = PointF(point.x - center.x, point.y - center.y)
        
        // 投影长度
        val projectionLength = toCursor.x * normalizedPerp.x + toCursor.y * normalizedPerp.y
        
        return PointF(
            center.x + normalizedPerp.x * projectionLength,
            center.y + normalizedPerp.y * projectionLength
        )
    }
    
    /**
     * 🔵 约束短轴端点到垂直方向
     */
    private fun constrainMinorAxisToPerpendicularDirection() {
        if (viewPoints.size < 3) return

        val center = viewPoints[0]
        val majorEnd = viewPoints[1]
        val currentMinorEnd = viewPoints[2]

        // 计算当前短轴长度
        val currentMinorLength = sqrt(
            (currentMinorEnd.x - center.x).pow(2) + (currentMinorEnd.y - center.y).pow(2)
        )

        // 计算新的垂直方向
        val majorVector = PointF(majorEnd.x - center.x, majorEnd.y - center.y)
        val perpVector = PointF(-majorVector.y, majorVector.x)

        // 归一化垂直向量
        val perpLength = sqrt(perpVector.x.pow(2) + perpVector.y.pow(2))
        if (perpLength > 0) {
            val normalizedPerp = PointF(perpVector.x / perpLength, perpVector.y / perpLength)

            // 保持短轴长度，更新到垂直方向
            viewPoints[2] = PointF(
                center.x + normalizedPerp.x * currentMinorLength,
                center.y + normalizedPerp.y * currentMinorLength
            )
        }
    }

    /**
     * 🔵 移动整个椭圆（拖动圆心时使用）
     */
    fun moveEntireEllipse(newCenter: PointF) {
        if (viewPoints.size < 3) return

        val currentCenter = viewPoints[0]
        val dx = newCenter.x - currentCenter.x
        val dy = newCenter.y - currentCenter.y

        // 移动所有控制点，保持椭圆形状不变
        viewPoints[0] = newCenter  // 新圆心
        viewPoints[1] = PointF(viewPoints[1].x + dx, viewPoints[1].y + dy)  // 长轴端点
        viewPoints[2] = PointF(viewPoints[2].x + dx, viewPoints[2].y + dy)  // 短轴端点

        invalidateCache()
        lastModified = System.currentTimeMillis()
    }

    /**
     * 🔵 计算文本显示位置（圆心下方固定偏移）
     */
    fun calculateTextPosition(): PointF {
        if (viewPoints.isEmpty()) return PointF(0f, 0f)

        val center = viewPoints[0]
        // 文本放在圆心下方80像素处，避免遮挡圆心
        return PointF(center.x, center.y + 80f)
    }
    
    /**
     * 🔵 清除计算缓存
     */
    private fun invalidateCache() {
        cachedArea = null
        cachedPerimeter = null
        cacheValidTime = 0L
    }
    
    /**
     * 🔵 检查点是否在触摸范围内
     */
    fun isPointInTouchRange(touchPoint: PointF, pointIndex: Int, touchRadius: Float): Boolean {
        // 修改为支持所有有效控制点索引（包括圆心索引0）
        if (pointIndex < 0 || pointIndex >= viewPoints.size || viewPoints.size < 3) return false

        val targetPoint = viewPoints[pointIndex]
        val distance = sqrt(
            (touchPoint.x - targetPoint.x).pow(2) + (touchPoint.y - targetPoint.y).pow(2)
        )
        return distance <= touchRadius
    }
    
    /**
     * 🔵 获取最近的控制点索引
     */
    fun getNearestControlPointIndex(touchPoint: PointF): Int {
        if (viewPoints.size < 3) return -1
        
        val majorEnd = viewPoints[1]
        val minorEnd = viewPoints[2]
        
        val distanceToMajor = sqrt(
            (touchPoint.x - majorEnd.x).pow(2) + (touchPoint.y - majorEnd.y).pow(2)
        )
        val distanceToMinor = sqrt(
            (touchPoint.x - minorEnd.x).pow(2) + (touchPoint.y - minorEnd.y).pow(2)
        )
        
        return if (distanceToMajor <= distanceToMinor) 1 else 2
    }
    
    /**
     * 🔄 同步位图坐标 - 从视图坐标转换到位图坐标
     */
    fun syncBitmapCoords(imageView: TpImageView) {
        bitmapPoints.clear()
        viewPoints.forEach { viewPoint ->
            val bitmapPoint = convertViewToBitmapCoords(viewPoint, imageView)
            bitmapPoints.add(bitmapPoint)
        }
        lastModified = System.currentTimeMillis()
    }
    
    /**
     * 🔄 同步视图坐标 - 从位图坐标转换到视图坐标
     */
    fun syncViewCoords(imageView: TpImageView) {
        viewPoints.clear()
        bitmapPoints.forEach { bitmapPoint ->
            val viewPoint = convertBitmapToViewCoords(bitmapPoint, imageView)
            viewPoints.add(viewPoint)
        }
        lastModified = System.currentTimeMillis()
    }
    
    /**
     * 🔄 视图坐标到位图坐标转换
     */
    private fun convertViewToBitmapCoords(viewPoint: PointF, imageView: TpImageView): PointF {
        val matrix = imageView.imageMatrix
        val inverseMatrix = Matrix()
        
        return if (matrix.invert(inverseMatrix)) {
            val point = FloatArray(2) { 0f }
            point[0] = viewPoint.x
            point[1] = viewPoint.y
            inverseMatrix.mapPoints(point)
            PointF(point[0], point[1])
        } else {
            // 如果矩阵不可逆，返回原坐标
            PointF(viewPoint.x, viewPoint.y)
        }
    }
    
    /**
     * 🔄 位图坐标到视图坐标转换
     */
    private fun convertBitmapToViewCoords(bitmapPoint: PointF, imageView: TpImageView): PointF {
        val matrix = imageView.imageMatrix
        val point = FloatArray(2) { 0f }
        point[0] = bitmapPoint.x
        point[1] = bitmapPoint.y
        matrix.mapPoints(point)
        return PointF(point[0], point[1])
    }
    
    /**
     * 🔵 验证椭圆几何有效性
     */
    fun validateEllipseGeometry(): Boolean {
        if (viewPoints.size < 3) return false

        val center = viewPoints[0]
        val majorEnd = viewPoints[1]
        val minorEnd = viewPoints[2]

        // 检查控制点是否重合
        val majorDistance = sqrt((majorEnd.x - center.x).pow(2) + (majorEnd.y - center.y).pow(2))
        val minorDistance = sqrt((minorEnd.x - center.x).pow(2) + (minorEnd.y - center.y).pow(2))

        // 最小半径限制
        val minRadius = 10f
        return majorDistance >= minRadius && minorDistance >= minRadius
    }
}

/**
 * 🔵 椭圆测量助手 - 专业级椭圆测量实现
 *
 * 核心特性：
 * - 长轴端点自由移动，支持椭圆任意角度旋转
 * - 短轴端点垂直约束，确保几何正确性
 * - 实时计算面积和周长
 * - 智能触摸检测和拖拽控制
 */
class EllipseMeasureHelper {
    companion object {
        private const val TAG = "EllipseMeasureHelper"

        // 交互参数 - 与其他Helper保持一致
        private const val TOUCH_RADIUS = 80f
        private const val CLICK_TOLERANCE = 20f
        private const val LONG_PRESS_DURATION = 800L
    }

    // 核心组件
    private lateinit var imageView: TpImageView
    private lateinit var originalBitmap: Bitmap
    private var isInitialized = false

    // 测量数据管理
    private val measurements = ArrayList<EllipseMeasurement>()
    private var selectedMeasurement: EllipseMeasurement? = null

    // 交互状态
    private var isDraggingPoint = false
    private var draggedPointIndex = -1
    private var longPressStartTime = 0L
    private var lastTouchX = 0f
    private var lastTouchY = 0f

    // 回调
    private var measurementUpdateCallback: (() -> Unit)? = null

    /**
     * 🔵 初始化椭圆测量助手
     */
    fun init(imageView: TpImageView, bitmap: Bitmap) {
        this.imageView = imageView
        this.originalBitmap = bitmap
        this.isInitialized = true
        measurements.clear()
        resetInteractionState()
        Log.d(TAG, "🔵 EllipseMeasureHelper initialized")
    }

    /**
     * 🔵 设置测量更新回调
     */
    fun setMeasurementUpdateCallback(callback: () -> Unit) {
        this.measurementUpdateCallback = callback
    }

    /**
     * 🔵 创建新椭圆测量
     */
    fun startNewMeasurement(): String {
        if (!isInitialized) {
            Log.w(TAG, "Helper not initialized")
            return ""
        }

        val centerX = imageView.width / 2f
        val centerY = imageView.height / 2f
        val defaultMajorRadius = min(imageView.width, imageView.height) / 6f
        val defaultMinorRadius = defaultMajorRadius * 0.6f

        val measurement = EllipseMeasurement().apply {
            viewPoints.addAll(listOf(
                PointF(centerX, centerY), // center
                PointF(centerX + defaultMajorRadius, centerY), // majorAxisEnd
                PointF(centerX, centerY + defaultMinorRadius)  // minorAxisEnd
            ))
            isCompleted = true
            isSelected = true
        }

        // 清除其他选中状态
        measurements.forEach {
            it.isSelected = false
            it.isEditing = false
        }

        measurements.add(measurement)
        measurement.syncBitmapCoords(imageView)
        measurementUpdateCallback?.invoke()

        Log.d(TAG, "✅ New ellipse measurement created, ID: ${measurement.id}")
        return measurement.id
    }

    /**
     * 🔵 重置交互状态
     */
    private fun resetInteractionState() {
        isDraggingPoint = false
        draggedPointIndex = -1
        longPressStartTime = 0L
        lastTouchX = 0f
        lastTouchY = 0f
    }

    /**
     * 🔵 获取测量数量
     */
    fun getMeasurementCount(): Int = measurements.size

    /**
     * 🔵 检查是否有选中的测量
     */
    fun hasSelectedMeasurement(): Boolean = selectedMeasurement != null

    /**
     * 🔵 检查是否靠近任何测量
     */
    fun isNearAnyMeasurement(touchPoint: PointF): Boolean {
        return measurements.any { measurement ->
            for (pointIndex in 1..2) {
                if (measurement.isPointInTouchRange(touchPoint, pointIndex, TOUCH_RADIUS)) {
                    return true
                }
            }
            false
        }
    }

    /**
     * 🔵 获取所有测量数据
     */
    fun getAllMeasurements(): List<EllipseMeasurement> = measurements.toList()

    /**
     * 🔵 清除所有选中状态
     */
    fun clearSelection() {
        measurements.forEach { it.isSelected = false }
        selectedMeasurement = null
        measurementUpdateCallback?.invoke()
    }

    /**
     * 🔵 处理触摸事件 - 核心交互逻辑
     */
    fun handleTouchEvent(event: MotionEvent, viewWidth: Int, viewHeight: Int): Boolean {
        if (!isInitialized) {
            Log.w(TAG, "⚠️ Helper not initialized, ignoring touch event")
            return false
        }

        val x = event.x
        val y = event.y
        val touchPoint = PointF(x, y)

        when (event.action) {
            MotionEvent.ACTION_DOWN -> {
                longPressStartTime = System.currentTimeMillis()
                lastTouchX = x
                lastTouchY = y

                Log.d(TAG, "🔵 ACTION_DOWN at ($x, $y)")

                // 检查端点触摸（优先级：圆心 > 长轴端点 > 短轴端点）
                for (measurement in measurements.reversed()) {
                    for (pointIndex in 0..2) { // 检查所有可拖拽的点：圆心、长轴端点、短轴端点
                        if (measurement.isPointInTouchRange(touchPoint, pointIndex, TOUCH_RADIUS)) {
                            selectedMeasurement = measurement
                            draggedPointIndex = pointIndex
                            isDraggingPoint = true

                            measurements.forEach { it.isSelected = false }
                            measurement.isSelected = true

                            val pointName = when(pointIndex) {
                                0 -> "center"
                                1 -> "major axis end"
                                2 -> "minor axis end"
                                else -> "unknown"
                            }
                            Log.d(TAG, "🎯 Started dragging $pointName (index $pointIndex) of measurement ${measurement.id}")
                            measurementUpdateCallback?.invoke()
                            return true
                        }
                    }
                }

                // 空白区域点击处理
                if (isInImageContentArea(touchPoint, viewWidth, viewHeight)) {
                    measurements.forEach { it.isSelected = false }
                    selectedMeasurement = null
                    Log.d(TAG, "🔵 Clicked in empty area, cleared selection")
                    measurementUpdateCallback?.invoke()
                }

                return false
            }

            MotionEvent.ACTION_MOVE -> {
                if (isDraggingPoint && selectedMeasurement != null && draggedPointIndex >= 0) {
                    when (draggedPointIndex) {
                        0 -> selectedMeasurement!!.moveEntireEllipse(touchPoint)  // 拖动圆心，移动整个椭圆
                        1 -> selectedMeasurement!!.updateMajorAxisEnd(touchPoint)
                        2 -> selectedMeasurement!!.updateMinorAxisEnd(touchPoint)
                    }
                    selectedMeasurement!!.syncBitmapCoords(imageView)
                    measurementUpdateCallback?.invoke()
                    return true
                }

                // 触摸恢复机制 - 检查是否重新接触到控制点
                else if (!isDraggingPoint && selectedMeasurement != null) {
                    for (measurement in measurements.reversed()) {
                        if (measurement == selectedMeasurement) {
                            for (pointIndex in 0..2) {  // 包含圆心的触摸恢复
                                if (measurement.isPointInTouchRange(touchPoint, pointIndex, TOUCH_RADIUS)) {
                                    draggedPointIndex = pointIndex
                                    isDraggingPoint = true

                                    // 立即处理这次移动
                                    when (pointIndex) {
                                        0 -> measurement.moveEntireEllipse(touchPoint)
                                        1 -> measurement.updateMajorAxisEnd(touchPoint)
                                        2 -> measurement.updateMinorAxisEnd(touchPoint)
                                    }
                                    measurement.syncBitmapCoords(imageView)
                                    measurementUpdateCallback?.invoke()

                                    val pointName = when(pointIndex) {
                                        0 -> "center"
                                        1 -> "major axis end"
                                        2 -> "minor axis end"
                                        else -> "unknown"
                                    }
                                    Log.d(TAG, "🔄 Touch recovery successful - resumed dragging $pointName (index $pointIndex)")
                                    return true
                                }
                            }
                        }
                    }
                }

                return false
            }

            MotionEvent.ACTION_UP -> {
                val touchDuration = System.currentTimeMillis() - longPressStartTime
                val touchDistance = sqrt((x - lastTouchX) * (x - lastTouchX) + (y - lastTouchY) * (y - lastTouchY))
                val wasDragging = isDraggingPoint

                Log.d(TAG, "🔵 ACTION_UP - duration: ${touchDuration}ms, distance: $touchDistance, wasDragging: $wasDragging")

                isDraggingPoint = false
                draggedPointIndex = -1

                // 长按删除逻辑
                if (!wasDragging && touchDistance < CLICK_TOLERANCE && touchDuration > LONG_PRESS_DURATION) {
                    Log.d(TAG, "🔍 Long press detected, checking for deletion...")

                    for (measurement in measurements.reversed()) {
                        for (pointIndex in 1..2) {
                            if (measurement.isPointInTouchRange(touchPoint, pointIndex, TOUCH_RADIUS)) {
                                measurements.remove(measurement)
                                if (selectedMeasurement == measurement) {
                                    selectedMeasurement = null
                                }
                                Log.d(TAG, "🗑️ Deleted measurement ${measurement.id} via long press")
                                measurementUpdateCallback?.invoke()
                                return true
                            }
                        }
                    }
                }

                return wasDragging
            }
        }
        return false
    }
    private fun isInImageContentArea(touchPoint: PointF, viewWidth: Int, viewHeight: Int): Boolean {
        try {
            // 定义UI区域边界（顶部和底部各20%为UI区域）
            val topUIHeight = viewHeight * 0.2f
            val bottomUIStart = viewHeight * 0.8f

            // 如果触摸点在顶部或底部UI区域，不取消选中
            if (touchPoint.y < topUIHeight || touchPoint.y > bottomUIStart) {
                return false
            }

            // 中间区域认为是图像内容区域
            return true

        } catch (e: Exception) {
            // 出错时保守处理，不清除选中状态
            return false
        }
    }

    /**
     * 🔵 删除选中的测量 - 智能删除逻辑
     */
    fun deleteSelectedMeasurement(): Boolean {
        var selected = selectedMeasurement

        // 自动选中逻辑
        if (selected == null && measurements.isNotEmpty()) {
            val lastMeasurement = measurements.last()
            lastMeasurement.isSelected = true
            selectedMeasurement = lastMeasurement
            selected = lastMeasurement
            Log.d(TAG, "🔄 Auto-selected last measurement for deletion: ${lastMeasurement.id}")
        }

        if (selected == null) {
            Log.w(TAG, "⚠️ No measurements available for deletion")
            return false
        }

        val removed = measurements.remove(selected)
        if (removed) {
            selectedMeasurement = null
            resetInteractionState()

            // 删除后重新选中
            if (measurements.isNotEmpty()) {
                val lastMeasurement = measurements.last()
                lastMeasurement.isSelected = true
                selectedMeasurement = lastMeasurement
                Log.d(TAG, "🎯 Auto-selected last measurement: ${lastMeasurement.id}")
            }

            measurementUpdateCallback?.invoke()
            Log.d(TAG, "✅ Measurement deleted successfully: ${selected.id}")
            return true
        }
        return false
    }

    /**
     * 🔵 清除所有测量
     */
    fun clearAllMeasurements() {
        measurements.clear()
        selectedMeasurement = null
        resetInteractionState()
        measurementUpdateCallback?.invoke()
        Log.d(TAG, "🧹 All ellipse measurements cleared")
    }

    /**
     * 🔵 状态恢复机制
     */
    fun recoverFromInvalidState() {
        try {
            val invalidMeasurements = measurements.filter { !it.validateEllipseGeometry() }

            if (invalidMeasurements.isNotEmpty()) {
                Log.w(TAG, "⚠️ Found ${invalidMeasurements.size} invalid ellipse measurements, recovering...")

                invalidMeasurements.forEach { measurement ->
                    // 恢复到默认状态
                    val center = measurement.viewPoints.getOrNull(0) ?: PointF(500f, 500f)
                    measurement.viewPoints.clear()
                    measurement.viewPoints.addAll(listOf(
                        center,
                        PointF(center.x + 100f, center.y),
                        PointF(center.x, center.y + 60f)
                    ))

                    measurement.syncBitmapCoords(imageView)
                    Log.d(TAG, "🔧 Recovered measurement: ${measurement.id}")
                }

                measurementUpdateCallback?.invoke()
            }
        } catch (e: Exception) {
            Log.e(TAG, "❌ Error in state recovery: ${e.message}")
        }
    }

    /**
     * 🔵 同步所有测量的坐标（缩放时调用）
     */
    fun syncAllCoordinates() {
        measurements.forEach { measurement ->
            measurement.syncViewCoords(imageView)
        }
        measurementUpdateCallback?.invoke()
        Log.d(TAG, "🔄 Synced coordinates for ${measurements.size} ellipse measurements")
    }

    /**
     * 🔵 获取测量统计信息
     */
    fun getMeasurementStats(): String {
        if (measurements.isEmpty()) return "无椭圆测量"

        val totalArea = measurements.sumOf { it.calculateArea() }
        val totalPerimeter = measurements.sumOf { it.calculatePerimeter() }

        return "椭圆测量: ${measurements.size}个, 总面积: ${String.format("%.2f", totalArea)}, 总周长: ${String.format("%.2f", totalPerimeter)}"
    }

    /**
     * 🎯 检查是否正在拖拽点
     */
    fun isDraggingPoint(): Boolean {
        return isDraggingPoint
    }

    /**
     * 🔄 缩放变化时同步坐标
     */
    fun onScaleChanged() {
        if (!isInitialized) return

        try {
            Log.d(TAG, "🔄 Scale changed - syncing ${measurements.size} ellipse measurements")
            measurements.forEach { measurement ->
                measurement.syncViewCoords(imageView)
            }
            measurementUpdateCallback?.invoke()
            Log.d(TAG, "🔄 Ellipse coordinates synced after scale change")
        } catch (e: Exception) {
            Log.e(TAG, "❌ Error syncing ellipse coordinates: ${e.message}")
        }
    }
}
