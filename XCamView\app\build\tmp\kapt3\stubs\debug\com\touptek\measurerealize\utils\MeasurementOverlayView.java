package com.touptek.measurerealize.utils;

import java.lang.System;

/**
 * 🎨 专业级测量覆盖层 - 超越iPad体验的可视化
 *
 * 核心特性：
 * - 多层测量渲染：支持同时显示多种测量类型
 * - 专业视觉效果：动态高亮、流畅动画、精美渲染
 * - 智能坐标转换：与TpImageView完美协作
 * - 高性能绘制：优化的Canvas绘制算法
 */
@kotlin.Metadata(mv = {1, 7, 1}, k = 1, d1 = {"\u0000\u009a\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\b\n\u0002\b\u0002\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0010\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0010\u0006\n\u0000\n\u0002\u0010\u000b\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\n\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u001f\u0018\u00002\u00020\u0001B%\b\u0007\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\n\b\u0002\u0010\u0004\u001a\u0004\u0018\u00010\u0005\u0012\b\b\u0002\u0010\u0006\u001a\u00020\u0007\u00a2\u0006\u0002\u0010\bJ \u0010>\u001a\u00020?2\u0006\u0010@\u001a\u00020?2\u0006\u0010A\u001a\u00020?2\u0006\u0010B\u001a\u00020?H\u0002J\u0006\u0010C\u001a\u00020DJD\u0010E\u001a\u00020D2\u0006\u0010F\u001a\u00020G2\u0006\u0010H\u001a\u00020?2\u0006\u0010I\u001a\u00020?2\u0006\u0010J\u001a\u00020?2\u0006\u0010K\u001a\u00020L2\b\b\u0002\u0010M\u001a\u00020N2\b\b\u0002\u0010O\u001a\u00020NH\u0002J \u0010P\u001a\u00020D2\u0006\u0010F\u001a\u00020G2\u0006\u0010Q\u001a\u00020\u00132\u0006\u0010)\u001a\u00020*H\u0002J2\u0010R\u001a\u00020D2\u0006\u0010F\u001a\u00020G2\u0006\u0010H\u001a\u00020?2\u0006\u0010K\u001a\u00020L2\u0006\u0010O\u001a\u00020N2\b\b\u0002\u0010M\u001a\u00020NH\u0002J \u0010S\u001a\u00020D2\u0006\u0010F\u001a\u00020G2\u0006\u0010Q\u001a\u00020T2\u0006\u0010)\u001a\u00020*H\u0002J \u0010U\u001a\u00020D2\u0006\u0010F\u001a\u00020G2\u0006\u0010Q\u001a\u00020V2\u0006\u0010)\u001a\u00020*H\u0002J \u0010W\u001a\u00020D2\u0006\u0010F\u001a\u00020G2\u0006\u0010Q\u001a\u00020X2\u0006\u0010)\u001a\u00020*H\u0002J \u0010Y\u001a\u00020D2\u0006\u0010F\u001a\u00020G2\u0006\u0010Q\u001a\u00020\u000b2\u0006\u0010)\u001a\u00020*H\u0002JB\u0010Z\u001a\u00020D2\u0006\u0010F\u001a\u00020G2\f\u0010[\u001a\b\u0012\u0004\u0012\u00020?0\n2\u0006\u0010\\\u001a\u00020?2\u0006\u0010K\u001a\u00020L2\b\b\u0002\u0010M\u001a\u00020N2\b\b\u0002\u0010O\u001a\u00020NH\u0002J \u0010]\u001a\u00020D2\u0006\u0010F\u001a\u00020G2\u0006\u0010Q\u001a\u00020\r2\u0006\u0010)\u001a\u00020*H\u0002J2\u0010^\u001a\u00020D2\u0006\u0010F\u001a\u00020G2\u0006\u0010\\\u001a\u00020?2\u0006\u0010K\u001a\u00020L2\u0006\u0010O\u001a\u00020N2\b\b\u0002\u0010M\u001a\u00020NH\u0002J8\u0010_\u001a\u00020D2\u0006\u0010F\u001a\u00020G2\f\u0010[\u001a\b\u0012\u0004\u0012\u00020?0\n2\b\u0010\\\u001a\u0004\u0018\u00010?2\u0006\u0010M\u001a\u00020N2\u0006\u0010O\u001a\u00020NH\u0002J \u0010`\u001a\u00020D2\u0006\u0010F\u001a\u00020G2\u0006\u0010Q\u001a\u00020\u000f2\u0006\u0010)\u001a\u00020*H\u0002J \u0010a\u001a\u00020D2\u0006\u0010F\u001a\u00020G2\u0006\u0010Q\u001a\u00020\u00112\u0006\u0010)\u001a\u00020*H\u0002J \u0010b\u001a\u00020D2\u0006\u0010F\u001a\u00020G2\u0006\u0010Q\u001a\u00020c2\u0006\u0010)\u001a\u00020*H\u0002J \u0010d\u001a\u00020D2\u0006\u0010F\u001a\u00020G2\u0006\u0010Q\u001a\u00020\u00152\u0006\u0010)\u001a\u00020*H\u0002J \u0010e\u001a\u00020D2\u0006\u0010F\u001a\u00020G2\u0006\u0010Q\u001a\u00020\u00172\u0006\u0010)\u001a\u00020*H\u0002J \u0010f\u001a\u00020D2\u0006\u0010F\u001a\u00020G2\u0006\u0010Q\u001a\u00020\u00192\u0006\u0010)\u001a\u00020*H\u0002J \u0010g\u001a\u00020D2\u0006\u0010F\u001a\u00020G2\u0006\u0010Q\u001a\u00020h2\u0006\u0010)\u001a\u00020*H\u0002J \u0010i\u001a\u00020D2\u0006\u0010F\u001a\u00020G2\u0006\u0010Q\u001a\u00020j2\u0006\u0010)\u001a\u00020*H\u0002J \u0010k\u001a\u00020D2\u0006\u0010F\u001a\u00020G2\u0006\u0010Q\u001a\u00020\u001b2\u0006\u0010)\u001a\u00020*H\u0002J \u0010l\u001a\u00020D2\u0006\u0010F\u001a\u00020G2\u0006\u0010Q\u001a\u00020\u001d2\u0006\u0010)\u001a\u00020mH\u0002J \u0010n\u001a\u00020D2\u0006\u0010F\u001a\u00020G2\u0006\u0010Q\u001a\u00020\u001f2\u0006\u0010)\u001a\u00020*H\u0002J\u0010\u0010o\u001a\u00020D2\u0006\u0010p\u001a\u00020qH\u0002J\u0010\u0010r\u001a\u00020D2\u0006\u0010F\u001a\u00020GH\u0014J\u0010\u0010s\u001a\u00020N2\u0006\u0010p\u001a\u00020qH\u0016J\u0014\u0010t\u001a\u00020D2\f\u0010u\u001a\b\u0012\u0004\u0012\u00020\u00130\nJ\u0014\u0010v\u001a\u00020D2\f\u0010u\u001a\b\u0012\u0004\u0012\u00020\r0\nJ\u0014\u0010w\u001a\u00020D2\f\u0010u\u001a\b\u0012\u0004\u0012\u00020\u001d0\nJ\u000e\u0010x\u001a\u00020D2\u0006\u0010y\u001a\u00020!J\u000e\u0010z\u001a\u00020D2\u0006\u0010y\u001a\u00020%J\u000e\u0010{\u001a\u00020D2\u0006\u0010y\u001a\u00020(J\u000e\u0010|\u001a\u00020D2\u0006\u0010)\u001a\u00020*J\u000e\u0010}\u001a\u00020D2\u0006\u0010y\u001a\u00020,J\u0010\u0010~\u001a\u00020D2\b\u0010Q\u001a\u0004\u0018\u00010/J\u000e\u0010\u007f\u001a\u00020D2\u0006\u0010y\u001a\u000201J\u000f\u0010\u0080\u0001\u001a\u00020D2\u0006\u0010y\u001a\u000203J\u000f\u0010\u0081\u0001\u001a\u00020D2\u0006\u0010y\u001a\u000206J\u000f\u0010\u0082\u0001\u001a\u00020D2\u0006\u0010y\u001a\u000209J\u000f\u0010\u0083\u0001\u001a\u00020D2\u0006\u0010y\u001a\u00020;J\u000f\u0010\u0084\u0001\u001a\u00020D2\u0006\u0010y\u001a\u00020=J\u0015\u0010\u0085\u0001\u001a\u00020D2\f\u0010u\u001a\b\u0012\u0004\u0012\u00020\u00130\nJ\u0015\u0010\u0086\u0001\u001a\u00020D2\f\u0010u\u001a\b\u0012\u0004\u0012\u00020\u000b0\nJ\u0015\u0010\u0087\u0001\u001a\u00020D2\f\u0010u\u001a\b\u0012\u0004\u0012\u00020\r0\nJ\u0015\u0010\u0088\u0001\u001a\u00020D2\f\u0010u\u001a\b\u0012\u0004\u0012\u00020\u000f0\nJ\u0015\u0010\u0089\u0001\u001a\u00020D2\f\u0010u\u001a\b\u0012\u0004\u0012\u00020\u00110\nJ\u0015\u0010\u008a\u0001\u001a\u00020D2\f\u0010u\u001a\b\u0012\u0004\u0012\u00020\u00150\nJ\u0015\u0010\u008b\u0001\u001a\u00020D2\f\u0010u\u001a\b\u0012\u0004\u0012\u00020\u00170\nJ\u0015\u0010\u008c\u0001\u001a\u00020D2\f\u0010u\u001a\b\u0012\u0004\u0012\u00020\u00190\nJ\u0015\u0010\u008d\u0001\u001a\u00020D2\f\u0010u\u001a\b\u0012\u0004\u0012\u00020\u001b0\nJ\u0015\u0010\u008e\u0001\u001a\u00020D2\f\u0010u\u001a\b\u0012\u0004\u0012\u00020\u001d0\nJ\u0015\u0010\u008f\u0001\u001a\u00020D2\f\u0010u\u001a\b\u0012\u0004\u0012\u00020\u001f0\nR\u0014\u0010\t\u001a\b\u0012\u0004\u0012\u00020\u000b0\nX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0014\u0010\f\u001a\b\u0012\u0004\u0012\u00020\r0\nX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0014\u0010\u000e\u001a\b\u0012\u0004\u0012\u00020\u000f0\nX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0014\u0010\u0010\u001a\b\u0012\u0004\u0012\u00020\u00110\nX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0014\u0010\u0012\u001a\b\u0012\u0004\u0012\u00020\u00130\nX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0014\u0010\u0014\u001a\b\u0012\u0004\u0012\u00020\u00150\nX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0014\u0010\u0016\u001a\b\u0012\u0004\u0012\u00020\u00170\nX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0014\u0010\u0018\u001a\b\u0012\u0004\u0012\u00020\u00190\nX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0014\u0010\u001a\u001a\b\u0012\u0004\u0012\u00020\u001b0\nX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0014\u0010\u001c\u001a\b\u0012\u0004\u0012\u00020\u001d0\nX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0014\u0010\u001e\u001a\b\u0012\u0004\u0012\u00020\u001f0\nX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0010\u0010 \u001a\u0004\u0018\u00010!X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\"\u001a\u00020#X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0010\u0010$\u001a\u0004\u0018\u00010%X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010&\u001a\u00020#X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0010\u0010\'\u001a\u0004\u0018\u00010(X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0010\u0010)\u001a\u0004\u0018\u00010*X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0010\u0010+\u001a\u0004\u0018\u00010,X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010-\u001a\u00020#X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0010\u0010.\u001a\u0004\u0018\u00010/X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0010\u00100\u001a\u0004\u0018\u000101X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0010\u00102\u001a\u0004\u0018\u000103X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u00104\u001a\u00020#X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0010\u00105\u001a\u0004\u0018\u000106X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u00107\u001a\u00020#X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0010\u00108\u001a\u0004\u0018\u000109X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0010\u0010:\u001a\u0004\u0018\u00010;X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0010\u0010<\u001a\u0004\u0018\u00010=X\u0082\u000e\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u0090\u0001"}, d2 = {"Lcom/touptek/measurerealize/utils/MeasurementOverlayView;", "Landroid/view/View;", "context", "Landroid/content/Context;", "attrs", "Landroid/util/AttributeSet;", "defStyleAttr", "", "(Landroid/content/Context;Landroid/util/AttributeSet;I)V", "allEllipseMeasurementData", "", "Lcom/touptek/measurerealize/utils/EllipseMeasurement;", "allFourPointAngleMeasurementData", "Lcom/touptek/measurerealize/utils/FourPointAngleMeasurementData;", "allHorizonLineMeasurementData", "Lcom/touptek/measurerealize/utils/HorizonLineMeasurementData;", "allLineMeasurementData", "Lcom/touptek/measurerealize/utils/LineMeasurementData;", "allMeasurementData", "Lcom/touptek/measurerealize/utils/AngleMeasurementData;", "allParallelLinesMeasurementData", "Lcom/touptek/measurerealize/utils/ParallelLinesMeasurementData;", "allPointMeasurementData", "Lcom/touptek/measurerealize/utils/PointMeasurementData;", "allRectangleMeasurementData", "Lcom/touptek/measurerealize/utils/RectangleMeasurementData;", "allThreeRectangleMeasurementData", "Lcom/touptek/measurerealize/utils/ThreeRectangleMeasurementData;", "allThreeVerticalMeasurementData", "Lcom/touptek/measurerealize/utils/ThreeVerticalMeasurementData;", "allVerticalLineMeasurementData", "Lcom/touptek/measurerealize/utils/VerticalLineMeasurementData;", "angleMeasureHelper", "Lcom/touptek/measurerealize/utils/AngleMeasureHelper;", "arcPaint", "Landroid/graphics/Paint;", "fourPointAngleHelper", "Lcom/touptek/measurerealize/utils/FourPointAngleHelper;", "highlightPointPaint", "horizonLineMeasureHelper", "Lcom/touptek/measurerealize/utils/HorizonLineMeasureHelper;", "imageView", "Lcom/touptek/measurerealize/TpImageView;", "lineMeasureHelper", "Lcom/touptek/measurerealize/utils/LineMeasureHelper;", "linePaint", "measurementData", "Lcom/touptek/measurerealize/utils/MeasurementData;", "parallelLinesMeasureHelper", "Lcom/touptek/measurerealize/utils/ParallelLinesMeasureHelper;", "pointMeasureHelper", "Lcom/touptek/measurerealize/utils/PointMeasureHelper;", "pointPaint", "rectangleMeasureHelper", "Lcom/touptek/measurerealize/utils/RectangleMeasureHelper;", "textPaint", "threeRectangleMeasureHelper", "Lcom/touptek/measurerealize/utils/ThreeRectangleMeasureHelper;", "threeVerticalMeasureHelper", "Lcom/touptek/measurerealize/utils/ThreeVerticalMeasureHelper;", "verticalLineMeasureHelper", "Lcom/touptek/measurerealize/utils/VerticalLineMeasureHelper;", "calculatePerpendicularFoot", "Landroid/graphics/PointF;", "point", "lineStart", "lineEnd", "clearMeasurement", "", "drawAngleArc", "canvas", "Landroid/graphics/Canvas;", "vertex", "point1", "point2", "angle", "", "isSelected", "", "isDragging", "drawAngleMeasurement", "data", "drawAngleText", "drawCircleMeasurement", "Lcom/touptek/measurerealize/utils/CircleMeasurementData;", "drawDistanceMeasurement", "Lcom/touptek/measurerealize/utils/DistanceMeasurementData;", "drawEllipseMeasurement", "Lcom/touptek/measurerealize/utils/EllipseMeasurementData;", "drawEllipseMeasurementFromHelper", "drawFourPointAngleArc", "points", "intersection", "drawFourPointAngleMeasurement", "drawFourPointAngleText", "drawFourPointExtensionLines", "drawHorizonLineMeasurement", "drawLineMeasurement", "drawMultiPointPathMeasurement", "Lcom/touptek/measurerealize/utils/MultiPointPathMeasurementData;", "drawParallelLinesMeasurement", "drawPointMeasurement", "drawRectangleMeasurement", "drawRectangleMeasurementFromHelper", "Lcom/touptek/measurerealize/utils/RectangleMeasurement;", "drawThreePointCircleMeasurement", "Lcom/touptek/measurerealize/utils/ThreePointCircleMeasurementData;", "drawThreeRectangleMeasurement", "drawThreeVerticalMeasurement", "Landroid/widget/ImageView;", "drawVerticalLineMeasurement", "forwardEventToImageView", "event", "Landroid/view/MotionEvent;", "onDraw", "onTouchEvent", "setAllAngleMeasurementData", "dataList", "setAllFourPointAngleMeasurementData", "setAllThreeVerticalMeasurementData", "setAngleMeasureHelper", "helper", "setFourPointAngleHelper", "setHorizonLineMeasureHelper", "setImageView", "setLineMeasureHelper", "setMeasurementData", "setParallelLinesMeasureHelper", "setPointMeasureHelper", "setRectangleMeasureHelper", "setThreeRectangleMeasureHelper", "setThreeVerticalMeasureHelper", "setVerticalLineMeasureHelper", "updateAngleMeasurements", "updateEllipseMeasurements", "updateFourPointAngleMeasurements", "updateHorizonLineMeasurements", "updateLineMeasurements", "updateParallelLinesMeasurements", "updatePointMeasurements", "updateRectangleMeasurements", "updateThreeRectangleMeasurements", "updateThreeVerticalMeasurements", "updateVerticalLineMeasurements", "app_debug"})
public final class MeasurementOverlayView extends android.view.View {
    private final android.graphics.Paint linePaint = null;
    private final android.graphics.Paint pointPaint = null;
    private final android.graphics.Paint highlightPointPaint = null;
    private final android.graphics.Paint textPaint = null;
    private final android.graphics.Paint arcPaint = null;
    private com.touptek.measurerealize.utils.MeasurementData measurementData;
    private java.util.List<com.touptek.measurerealize.utils.AngleMeasurementData> allMeasurementData;
    private java.util.List<com.touptek.measurerealize.utils.FourPointAngleMeasurementData> allFourPointAngleMeasurementData;
    private java.util.List<com.touptek.measurerealize.utils.PointMeasurementData> allPointMeasurementData;
    private java.util.List<com.touptek.measurerealize.utils.LineMeasurementData> allLineMeasurementData;
    private java.util.List<com.touptek.measurerealize.utils.HorizonLineMeasurementData> allHorizonLineMeasurementData;
    private java.util.List<com.touptek.measurerealize.utils.VerticalLineMeasurementData> allVerticalLineMeasurementData;
    private java.util.List<com.touptek.measurerealize.utils.ParallelLinesMeasurementData> allParallelLinesMeasurementData;
    private java.util.List<com.touptek.measurerealize.utils.ThreeVerticalMeasurementData> allThreeVerticalMeasurementData;
    private java.util.List<com.touptek.measurerealize.utils.RectangleMeasurementData> allRectangleMeasurementData;
    private java.util.List<com.touptek.measurerealize.utils.ThreeRectangleMeasurementData> allThreeRectangleMeasurementData;
    private java.util.List<com.touptek.measurerealize.utils.EllipseMeasurement> allEllipseMeasurementData;
    private com.touptek.measurerealize.TpImageView imageView;
    private com.touptek.measurerealize.utils.AngleMeasureHelper angleMeasureHelper;
    private com.touptek.measurerealize.utils.FourPointAngleHelper fourPointAngleHelper;
    private com.touptek.measurerealize.utils.PointMeasureHelper pointMeasureHelper;
    private com.touptek.measurerealize.utils.LineMeasureHelper lineMeasureHelper;
    private com.touptek.measurerealize.utils.HorizonLineMeasureHelper horizonLineMeasureHelper;
    private com.touptek.measurerealize.utils.VerticalLineMeasureHelper verticalLineMeasureHelper;
    private com.touptek.measurerealize.utils.ParallelLinesMeasureHelper parallelLinesMeasureHelper;
    private com.touptek.measurerealize.utils.ThreeVerticalMeasureHelper threeVerticalMeasureHelper;
    private com.touptek.measurerealize.utils.RectangleMeasureHelper rectangleMeasureHelper;
    private com.touptek.measurerealize.utils.ThreeRectangleMeasureHelper threeRectangleMeasureHelper;
    
    @kotlin.jvm.JvmOverloads
    public MeasurementOverlayView(@org.jetbrains.annotations.NotNull
    android.content.Context context) {
        super(null);
    }
    
    @kotlin.jvm.JvmOverloads
    public MeasurementOverlayView(@org.jetbrains.annotations.NotNull
    android.content.Context context, @org.jetbrains.annotations.Nullable
    android.util.AttributeSet attrs) {
        super(null);
    }
    
    @kotlin.jvm.JvmOverloads
    public MeasurementOverlayView(@org.jetbrains.annotations.NotNull
    android.content.Context context, @org.jetbrains.annotations.Nullable
    android.util.AttributeSet attrs, int defStyleAttr) {
        super(null);
    }
    
    public final void setImageView(@org.jetbrains.annotations.NotNull
    com.touptek.measurerealize.TpImageView imageView) {
    }
    
    public final void setAngleMeasureHelper(@org.jetbrains.annotations.NotNull
    com.touptek.measurerealize.utils.AngleMeasureHelper helper) {
    }
    
    public final void setFourPointAngleHelper(@org.jetbrains.annotations.NotNull
    com.touptek.measurerealize.utils.FourPointAngleHelper helper) {
    }
    
    public final void setPointMeasureHelper(@org.jetbrains.annotations.NotNull
    com.touptek.measurerealize.utils.PointMeasureHelper helper) {
    }
    
    public final void setLineMeasureHelper(@org.jetbrains.annotations.NotNull
    com.touptek.measurerealize.utils.LineMeasureHelper helper) {
    }
    
    public final void setHorizonLineMeasureHelper(@org.jetbrains.annotations.NotNull
    com.touptek.measurerealize.utils.HorizonLineMeasureHelper helper) {
    }
    
    public final void setVerticalLineMeasureHelper(@org.jetbrains.annotations.NotNull
    com.touptek.measurerealize.utils.VerticalLineMeasureHelper helper) {
    }
    
    public final void setParallelLinesMeasureHelper(@org.jetbrains.annotations.NotNull
    com.touptek.measurerealize.utils.ParallelLinesMeasureHelper helper) {
    }
    
    public final void setThreeVerticalMeasureHelper(@org.jetbrains.annotations.NotNull
    com.touptek.measurerealize.utils.ThreeVerticalMeasureHelper helper) {
    }
    
    public final void setRectangleMeasureHelper(@org.jetbrains.annotations.NotNull
    com.touptek.measurerealize.utils.RectangleMeasureHelper helper) {
    }
    
    public final void setThreeRectangleMeasureHelper(@org.jetbrains.annotations.NotNull
    com.touptek.measurerealize.utils.ThreeRectangleMeasureHelper helper) {
    }
    
    public final void setMeasurementData(@org.jetbrains.annotations.Nullable
    com.touptek.measurerealize.utils.MeasurementData data) {
    }
    
    public final void clearMeasurement() {
    }
    
    /**
     * 🎯 设置多个角度测量数据 - 支持同时显示多个角度
     */
    public final void setAllAngleMeasurementData(@org.jetbrains.annotations.NotNull
    java.util.List<com.touptek.measurerealize.utils.AngleMeasurementData> dataList) {
    }
    
    /**
     * 🎯 设置多个四点角度测量数据 - 支持同时显示多个四点角度
     */
    public final void setAllFourPointAngleMeasurementData(@org.jetbrains.annotations.NotNull
    java.util.List<com.touptek.measurerealize.utils.FourPointAngleMeasurementData> dataList) {
    }
    
    /**
     * 🎯 更新三点角度测量数据 - 混合模式支持
     */
    public final void updateAngleMeasurements(@org.jetbrains.annotations.NotNull
    java.util.List<com.touptek.measurerealize.utils.AngleMeasurementData> dataList) {
    }
    
    /**
     * 🎯 更新四点角度测量数据 - 混合模式支持
     */
    public final void updateFourPointAngleMeasurements(@org.jetbrains.annotations.NotNull
    java.util.List<com.touptek.measurerealize.utils.FourPointAngleMeasurementData> dataList) {
    }
    
    /**
     * 🎯 更新点测量数据 - 混合模式支持
     */
    public final void updatePointMeasurements(@org.jetbrains.annotations.NotNull
    java.util.List<com.touptek.measurerealize.utils.PointMeasurementData> dataList) {
    }
    
    /**
     * 📏 更新线段测量数据 - 混合模式支持
     */
    public final void updateLineMeasurements(@org.jetbrains.annotations.NotNull
    java.util.List<com.touptek.measurerealize.utils.LineMeasurementData> dataList) {
    }
    
    /**
     * 📏 更新水平线测量数据 - 混合模式支持
     */
    public final void updateHorizonLineMeasurements(@org.jetbrains.annotations.NotNull
    java.util.List<com.touptek.measurerealize.utils.HorizonLineMeasurementData> dataList) {
    }
    
    /**
     * 📏 更新垂直线测量数据 - 混合模式支持
     */
    public final void updateVerticalLineMeasurements(@org.jetbrains.annotations.NotNull
    java.util.List<com.touptek.measurerealize.utils.VerticalLineMeasurementData> dataList) {
    }
    
    /**
     * 📏 更新平行线测量数据 - 混合模式支持
     */
    public final void updateParallelLinesMeasurements(@org.jetbrains.annotations.NotNull
    java.util.List<com.touptek.measurerealize.utils.ParallelLinesMeasurementData> dataList) {
    }
    
    /**
     * 🎯 设置多个三垂直测量数据 - 支持同时显示多个三垂直测量
     */
    public final void setAllThreeVerticalMeasurementData(@org.jetbrains.annotations.NotNull
    java.util.List<com.touptek.measurerealize.utils.ThreeVerticalMeasurementData> dataList) {
    }
    
    /**
     * 📏 更新三垂直测量数据 - 混合模式支持
     */
    public final void updateThreeVerticalMeasurements(@org.jetbrains.annotations.NotNull
    java.util.List<com.touptek.measurerealize.utils.ThreeVerticalMeasurementData> dataList) {
    }
    
    /**
     * 📏 更新矩形测量数据 - 混合模式支持
     */
    public final void updateRectangleMeasurements(@org.jetbrains.annotations.NotNull
    java.util.List<com.touptek.measurerealize.utils.RectangleMeasurementData> dataList) {
    }
    
    /**
     * 📏 更新三点矩形测量数据 - 混合模式支持
     */
    public final void updateThreeRectangleMeasurements(@org.jetbrains.annotations.NotNull
    java.util.List<com.touptek.measurerealize.utils.ThreeRectangleMeasurementData> dataList) {
    }
    
    /**
     * 🔵 更新椭圆测量数据 - 混合模式支持
     */
    public final void updateEllipseMeasurements(@org.jetbrains.annotations.NotNull
    java.util.List<com.touptek.measurerealize.utils.EllipseMeasurement> dataList) {
    }
    
    @java.lang.Override
    protected void onDraw(@org.jetbrains.annotations.NotNull
    android.graphics.Canvas canvas) {
    }
    
    /**
     * 🎯 绘制角度测量 - 专业级三点角度可视化
     */
    private final void drawAngleMeasurement(android.graphics.Canvas canvas, com.touptek.measurerealize.utils.AngleMeasurementData data, com.touptek.measurerealize.TpImageView imageView) {
    }
    
    /**
     * 🎨 绘制角度弧线 - 专业级弧线渲染
     */
    private final void drawAngleArc(android.graphics.Canvas canvas, android.graphics.PointF vertex, android.graphics.PointF point1, android.graphics.PointF point2, double angle, boolean isSelected, boolean isDragging) {
    }
    
    /**
     * 🎨 绘制角度文本 - 智能位置和专业样式
     */
    private final void drawAngleText(android.graphics.Canvas canvas, android.graphics.PointF vertex, double angle, boolean isDragging, boolean isSelected) {
    }
    
    private final void drawDistanceMeasurement(android.graphics.Canvas canvas, com.touptek.measurerealize.utils.DistanceMeasurementData data, com.touptek.measurerealize.TpImageView imageView) {
    }
    
    private final void drawRectangleMeasurement(android.graphics.Canvas canvas, com.touptek.measurerealize.utils.RectangleMeasurementData data, com.touptek.measurerealize.TpImageView imageView) {
    }
    
    /**
     * 📐 绘制三点矩形测量 - 支持旋转的矩形
     */
    private final void drawThreeRectangleMeasurement(android.graphics.Canvas canvas, com.touptek.measurerealize.utils.ThreeRectangleMeasurementData data, com.touptek.measurerealize.TpImageView imageView) {
    }
    
    private final void drawCircleMeasurement(android.graphics.Canvas canvas, com.touptek.measurerealize.utils.CircleMeasurementData data, com.touptek.measurerealize.TpImageView imageView) {
    }
    
    private final void drawThreePointCircleMeasurement(android.graphics.Canvas canvas, com.touptek.measurerealize.utils.ThreePointCircleMeasurementData data, com.touptek.measurerealize.TpImageView imageView) {
    }
    
    private final void drawFourPointAngleMeasurement(android.graphics.Canvas canvas, com.touptek.measurerealize.utils.FourPointAngleMeasurementData data, com.touptek.measurerealize.TpImageView imageView) {
    }
    
    /**
     * 🎨 绘制四点角度弧线
     */
    private final void drawFourPointAngleArc(android.graphics.Canvas canvas, java.util.List<? extends android.graphics.PointF> points, android.graphics.PointF intersection, double angle, boolean isSelected, boolean isDragging) {
    }
    
    /**
     * 🎨 绘制四点角度文本
     */
    private final void drawFourPointAngleText(android.graphics.Canvas canvas, android.graphics.PointF intersection, double angle, boolean isDragging, boolean isSelected) {
    }
    
    private final void drawEllipseMeasurement(android.graphics.Canvas canvas, com.touptek.measurerealize.utils.EllipseMeasurementData data, com.touptek.measurerealize.TpImageView imageView) {
    }
    
    private final void drawMultiPointPathMeasurement(android.graphics.Canvas canvas, com.touptek.measurerealize.utils.MultiPointPathMeasurementData data, com.touptek.measurerealize.TpImageView imageView) {
    }
    
    /**
     * 🎯 处理触摸事件 - 委托给测量助手
     */
    @java.lang.Override
    public boolean onTouchEvent(@org.jetbrains.annotations.NotNull
    android.view.MotionEvent event) {
        return false;
    }
    
    /**
     * 🔄 将事件转发给TpImageView
     */
    private final void forwardEventToImageView(android.view.MotionEvent event) {
    }
    
    /**
     * 🎨 绘制四点角度智能延长线
     */
    private final void drawFourPointExtensionLines(android.graphics.Canvas canvas, java.util.List<? extends android.graphics.PointF> points, android.graphics.PointF intersection, boolean isSelected, boolean isDragging) {
    }
    
    /**
     * 🎯 绘制点测量 - 红色圆点+白色十字
     */
    private final void drawPointMeasurement(android.graphics.Canvas canvas, com.touptek.measurerealize.utils.PointMeasurementData data, com.touptek.measurerealize.TpImageView imageView) {
    }
    
    /**
     * 📏 绘制线段测量 - 与AngleMeasureHelper和PointMeasureHelper保持一致的状态视觉反馈
     */
    private final void drawLineMeasurement(android.graphics.Canvas canvas, com.touptek.measurerealize.utils.LineMeasurementData data, com.touptek.measurerealize.TpImageView imageView) {
    }
    
    /**
     * 📏 绘制水平线测量 - 专门的水平约束线段绘制
     */
    private final void drawHorizonLineMeasurement(android.graphics.Canvas canvas, com.touptek.measurerealize.utils.HorizonLineMeasurementData data, com.touptek.measurerealize.TpImageView imageView) {
    }
    
    /**
     * 📏 绘制垂直线测量 - 专门的垂直约束线段绘制
     */
    private final void drawVerticalLineMeasurement(android.graphics.Canvas canvas, com.touptek.measurerealize.utils.VerticalLineMeasurementData data, com.touptek.measurerealize.TpImageView imageView) {
    }
    
    /**
     * 📏 绘制平行线测量 - 专业级平行线可视化
     */
    private final void drawParallelLinesMeasurement(android.graphics.Canvas canvas, com.touptek.measurerealize.utils.ParallelLinesMeasurementData data, com.touptek.measurerealize.TpImageView imageView) {
    }
    
    /**
     * 🔧 计算垂足位置
     */
    private final android.graphics.PointF calculatePerpendicularFoot(android.graphics.PointF point, android.graphics.PointF lineStart, android.graphics.PointF lineEnd) {
        return null;
    }
    
    /**
     * 🎨 绘制三垂直测量 - 专业级渲染
     */
    private final void drawThreeVerticalMeasurement(android.graphics.Canvas canvas, com.touptek.measurerealize.utils.ThreeVerticalMeasurementData data, android.widget.ImageView imageView) {
    }
    
    /**
     * 📐 绘制矩形测量（从RectangleMeasureHelper获取的数据）
     */
    private final void drawRectangleMeasurementFromHelper(android.graphics.Canvas canvas, com.touptek.measurerealize.utils.RectangleMeasurement data, com.touptek.measurerealize.TpImageView imageView) {
    }
    
    /**
     * 🔵 绘制椭圆测量（从EllipseMeasureHelper获取的数据）
     */
    private final void drawEllipseMeasurementFromHelper(android.graphics.Canvas canvas, com.touptek.measurerealize.utils.EllipseMeasurement data, com.touptek.measurerealize.TpImageView imageView) {
    }
}