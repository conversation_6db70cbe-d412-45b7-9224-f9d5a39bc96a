package com.touptek.measurerealize.utils

import android.graphics.PointF

/**
 * 🎨 测量数据基类 - 专业级测量系统
 */
sealed class MeasurementData

/**
 * 🎯 角度测量数据 - 三点角度测量
 */
data class AngleMeasurementData(
    val points: List<PointF>,           // 三个点：顶点、第一点、第二点
    val angle: Double,                  // 计算出的角度值（度）
    val isDragging: Boolean = false,    // 是否正在拖拽状态
    val isSelected: Boolean = false     // 是否为选中状态
) : MeasurementData()

/**
 * 📏 距离测量数据 - 两点距离测量
 */
data class DistanceMeasurementData(
    val points: List<PointF>,           // 两个点：起点、终点
    val distance: Double,               // 计算出的距离值
    val isDragging: Boolean = false     // 是否正在拖拽状态
) : MeasurementData()

/**
 * ⭕ 圆形测量数据 - 圆心+半径点测量
 */
data class CircleMeasurementData(
    val points: List<PointF>,           // 两个点：圆心、半径点
    val radius: Double,                 // 半径
    val area: Double,                   // 面积
    val perimeter: Double,              // 周长
    val isDragging: Boolean = false     // 是否正在拖拽状态
) : MeasurementData()

/**
 * 🔵 中心圆测量数据 - 专业级圆心+半径点测量
 */
data class CenterCircleMeasurementData(
    val points: List<PointF>,           // 两个点：圆心、半径点
    val radius: Double,                 // 半径
    val area: Double,                   // 面积
    val perimeter: Double,              // 周长
    val isDragging: Boolean = false,    // 是否正在拖拽状态
    val draggedPointIndex: Int = -1,    // 被拖拽的点索引（0=圆心，1=半径点）
    val isPointHighlighted: Boolean = false,  // 是否高亮显示控制点
    val highlightedPointIndex: Int = -1,      // 高亮的点索引
    val textPosition: PointF = PointF()       // 文本显示位置
) : MeasurementData()

/**
 * 🔺 椭圆测量数据
 */
data class EllipseMeasurementData(
    val points: List<PointF>,           // 椭圆的关键点
    val area: Double,                   // 面积
    val perimeter: Double,              // 周长
    val isDragging: Boolean = false     // 是否正在拖拽状态
) : MeasurementData()

/**
 * 🔺 三点圆测量数据
 */
data class ThreePointCircleMeasurementData(
    val points: List<PointF>,           // 三个点确定圆
    val center: PointF,                 // 圆心
    val radius: Double,                 // 半径
    val area: Double,                   // 面积
    val perimeter: Double,              // 周长
    val isDragging: Boolean = false     // 是否正在拖拽状态
) : MeasurementData()

/**
 * 🔄 四点角度测量数据 - 增强版本
 */
data class FourPointAngleMeasurementData(
    val points: List<PointF>,           // 四个点构成两条线
    val intersection: PointF?,          // 两条线的交点
    val angle: Double,                  // 两条线的夹角
    val isValid: Boolean = true,        // 角度计算是否有效
    val isDragging: Boolean = false,    // 是否正在拖拽状态
    val isSelected: Boolean = false     // 是否为选中状态
) : MeasurementData()

/**
 * 📏 平行线测量数据
 */
data class ParallelLinesMeasurementData(
    val id: String,                     // 测量ID
    val viewPoints: List<PointF>,       // 四个端点：[line1_start, line1_end, line2_start, line2_end] 视图坐标
    val bitmapPoints: List<PointF>,     // 四个端点：[line1_start, line1_end, line2_start, line2_end] 位图坐标
    val isSelected: Boolean = false,    // 是否为选中状态
    val isEditing: Boolean = false,     // 是否正在编辑状态
    val isCompleted: Boolean = false,   // 是否已完成
    val textPosition: PointF? = null,   // 距离文本位置
    val distance: Double = 0.0          // 平行线间距离（像素）
) : MeasurementData()

/**
 * 📊 多点路径测量数据
 */
data class MultiPointPathMeasurementData(
    val points: List<PointF>,           // 路径上的所有点
    val totalLength: Double,            // 总长度
    val isDragging: Boolean = false     // 是否正在拖拽状态
) : MeasurementData()

/**
 * 🎯 点测量数据 - 单点标记测量
 */
data class PointMeasurementData(
    val points: List<PointF>,           // 点坐标（单个点）
    val isDragging: Boolean = false,    // 是否正在拖拽状态
    val isSelected: Boolean = false     // 是否为选中状态
) : MeasurementData()

/**
 * 📏 线段测量数据 - 双端点线段测量
 */
data class LineMeasurementData(
    val points: List<PointF>,           // 两个端点：[起点, 终点]
    val length: Double,                 // 线段长度（像素）
    val isDragging: Boolean = false,    // 是否正在拖拽状态
    val isSelected: Boolean = false     // 是否为选中状态
) : MeasurementData()

/**
 * 📏 水平线测量数据 - 水平约束线段测量
 */
data class HorizonLineMeasurementData(
    val id: String,                     // 测量ID
    val viewPoints: List<PointF>,       // 两个端点：[左端点, 右端点] 视图坐标
    val bitmapPoints: List<PointF>,     // 两个端点：[左端点, 右端点] 位图坐标
    val isSelected: Boolean = false,    // 是否为选中状态
    val isEditing: Boolean = false,     // 是否正在编辑状态
    val isCompleted: Boolean = false,   // 是否已完成
    val textPosition: PointF? = null,   // 长度文本位置
    val baselineY: Float = 0f,          // 基准水平线Y坐标
    val length: Double = 0.0            // 水平线长度（像素）
) : MeasurementData()

/**
 * 📏 垂直线测量数据 - 垂直约束线段测量
 */
data class VerticalLineMeasurementData(
    val id: String,                     // 测量ID
    val viewPoints: List<PointF>,       // 两个端点：[上端点, 下端点] 视图坐标
    val bitmapPoints: List<PointF>,     // 两个端点：[上端点, 下端点] 位图坐标
    val isSelected: Boolean = false,    // 是否为选中状态
    val isEditing: Boolean = false,     // 是否正在编辑状态
    val isCompleted: Boolean = false,   // 是否已完成
    val textPosition: PointF? = null,   // 长度文本位置
    val baselineX: Float = 0f,          // 基准垂直线X坐标
    val length: Double = 0.0            // 垂直线长度（像素）
) : MeasurementData()

/**
 * 📏 三垂直测量数据 - 点到线段的垂直距离测量
 */
data class ThreeVerticalMeasurementData(
    val id: String,                     // 测量ID
    val viewPoints: List<PointF>,       // 三个点：[基准线起点, 基准线终点, 独立点] 视图坐标
    val bitmapPoints: List<PointF>,     // 三个点：[基准线起点, 基准线终点, 独立点] 位图坐标
    val isSelected: Boolean = false,    // 是否为选中状态
    val isEditing: Boolean = false,     // 是否正在编辑状态
    val isCompleted: Boolean = false,   // 是否已完成
    val textPosition: PointF? = null,   // 距离文本位置
    val perpendicularFoot: PointF? = null, // 垂足位置（实时计算）
    val distance: Double = 0.0          // 垂直距离（像素）
) : MeasurementData()

/**
 * 📦 矩形测量数据 - 四角点矩形测量
 */
data class RectangleMeasurementData(
    val id: String,                     // 测量ID
    val viewPoints: List<PointF>,       // 四个角点：[左上, 右上, 右下, 左下] 视图坐标
    val bitmapPoints: List<PointF>,     // 四个角点：[左上, 右上, 右下, 左下] 位图坐标
    val isSelected: Boolean = false,    // 是否为选中状态
    val isEditing: Boolean = false,     // 是否正在编辑状态
    val isCompleted: Boolean = false,   // 是否已完成
    val textPosition: PointF? = null,   // 数值文本位置
    val area: Double = 0.0,             // 矩形面积（像素²）
    val perimeter: Double = 0.0,        // 矩形周长（像素）
    val isDragging: Boolean = false,    // 是否正在拖拽状态
    val draggedPointIndex: Int = -1     // 被拖拽的控制点索引（0=左上角, 2=右下角）
) : MeasurementData()

/**
 * 🎯 三点矩形测量数据 - 支持旋转的矩形测量
 */
data class ThreeRectangleMeasurementData(
    val id: String,                         // 测量ID
    val centerPoint: PointF,                // 中心点（视图坐标）
    val width: Float,                       // 宽度
    val height: Float,                      // 高度
    val rotationAngle: Float,               // 旋转角度（弧度）
    val controlPoints: List<PointF>,        // [左上角, 右上角, 右下角] 控制点
    val rotatedCorners: List<PointF>,       // 旋转后的四个角点
    val isSelected: Boolean = false,        // 是否为选中状态
    val isEditing: Boolean = false,         // 是否正在编辑状态
    val isCompleted: Boolean = false,       // 是否已完成
    val textPosition: PointF? = null,       // 数值文本位置
    val area: Double = 0.0,                 // 矩形面积（像素²）
    val perimeter: Double = 0.0,            // 矩形周长（像素）
    val isDragging: Boolean = false,        // 是否正在拖拽状态
    val draggedPointIndex: Int = -1         // 被拖拽的控制点索引（0=左上角, 1=右上角旋转, 2=右下角）
) : MeasurementData()
